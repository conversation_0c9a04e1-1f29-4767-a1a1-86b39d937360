from odoo import models, fields, api, _


class BSSICPermissionRequest(models.Model):
    _name = 'bssic.permission.request'
    _description = 'BSIC Permission Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    # Basic Information
    name = fields.Char('Request Reference', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                   tracking=True, ondelete="restrict")
    employee_number = fields.Char(string='Employee Number (ID)', tracking=True,
                                help="Enter employee ID number to automatically fetch employee details")
    department_id = fields.Many2one(related='employee_id.department_id',
                                   string='Department', store=True, readonly=True)
    job_id = fields.Many2one(related='employee_id.job_id', string='Job Position',
                            store=True, readonly=True)
    job_title = fields.Char(related='employee_id.job_title', string='Job Title',
                           store=True, readonly=True)
    request_date = fields.Date('Request Date', default=fields.Date.context_today,
                              required=True, tracking=True)
    description = fields.Text('Description', tracking=True)

    # State
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager_approval', 'Direct Manager Approval'),
        ('dept_manager_approval', 'Department Manager Approval'),
        ('it_manager_approval', 'IT Manager Approval'),
        ('assigned', 'Assigned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    # Activity Log
    activity_log_ids = fields.One2many('bssic.request.activity.log', 'permission_request_id', string='Activity Log')

    # Assignment
    assigned_to = fields.Many2one('hr.employee', string='Assigned To',
                                 tracking=True, ondelete="set null",
                                 domain="[('department_id.name', 'ilike', 'IT')]")

    # Permission Details
    permission_type = fields.Selection([
        ('add', 'Add'),
        ('modify', 'Modify'),
        ('delete', 'Delete'),
        ('withdraw', 'Withdraw'),
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
    ], string='Permission Type', tracking=True)
    user_name = fields.Char('User Name', tracking=True)
    validity_from = fields.Date('Valid From', tracking=True)
    validity_to = fields.Date('Valid To', tracking=True)

    # Department permissions
    accounting_dept = fields.Boolean(string='Accounting Department', tracking=True)
    accounting_level = fields.Char(string='Accounting Level', tracking=True)
    accounting_group = fields.Char(string='Accounting Group', tracking=True)

    internal_audit = fields.Boolean(string='Internal Auditing', tracking=True)
    internal_audit_level = fields.Char(string='Internal Audit Level', tracking=True)
    internal_audit_group = fields.Char(string='Internal Audit Group', tracking=True)

    risk_dept = fields.Boolean(string='Risk', tracking=True)
    risk_level = fields.Char(string='Risk Level', tracking=True)
    risk_group = fields.Char(string='Risk Group', tracking=True)

    back_office_credits = fields.Boolean(string='Back Office - Credits', tracking=True)
    back_office_credits_level = fields.Char(string='Credits Level', tracking=True)
    back_office_credits_group = fields.Char(string='Credits Group', tracking=True)

    back_office_deposits = fields.Boolean(string='Back Office - Deposits', tracking=True)
    back_office_deposits_level = fields.Char(string='Deposits Level', tracking=True)
    back_office_deposits_group = fields.Char(string='Deposits Group', tracking=True)

    operations_dept = fields.Boolean(string='Operations Department', tracking=True)
    operations_level = fields.Char(string='Operations Level', tracking=True)
    operations_group = fields.Char(string='Operations Group', tracking=True)

    forex_exchange = fields.Boolean(string='Forex Exchange', tracking=True)
    forex_level = fields.Char(string='Forex Level', tracking=True)
    forex_group = fields.Char(string='Forex Group', tracking=True)

    banking_operations = fields.Boolean(string='Banking Operations', tracking=True)
    banking_level = fields.Char(string='Banking Level', tracking=True)
    banking_group = fields.Char(string='Banking Group', tracking=True)

    personnel_admin = fields.Boolean(string='Personnel & Admin', tracking=True)
    personnel_level = fields.Char(string='Personnel Level', tracking=True)
    personnel_group = fields.Char(string='Personnel Group', tracking=True)

    swift = fields.Boolean(string='Swift', tracking=True)
    swift_level = fields.Char(string='Swift Level', tracking=True)
    swift_group = fields.Char(string='Swift Group', tracking=True)

    # Transaction limits
    transaction_amount_limit = fields.Float(string='Transaction Amount Limit', tracking=True)
    auth_limit = fields.Float(string='Auth. O.D. Limit', tracking=True)
    max_amount_limit = fields.Float(string='MAX Amount Limit', tracking=True)

    @api.model
    def create(self, vals):
        # Set sequence number
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('bssic.permission.request') or _('New')
        return super(BSSICPermissionRequest, self).create(vals)

    def action_submit(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'submitted',
                notes=_('Permission request submitted for approval'),
                old_state='draft', new_state='submitted'
            )
        self.write({'state': 'submitted'})

    def action_direct_manager_approve(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'direct_manager_approved',
                notes=_('Approved by Direct Manager'),
                old_state='submitted', new_state='direct_manager_approval'
            )
        self.write({'state': 'direct_manager_approval'})

    def action_dept_manager_approve(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'direct_manager_approved',
                notes=_('Approved by Department Manager'),
                old_state='direct_manager_approval', new_state='dept_manager_approval'
            )
        self.write({'state': 'dept_manager_approval'})

    def action_it_manager_approve(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'it_manager_approved',
                notes=_('Approved by IT Manager'),
                old_state='dept_manager_approval', new_state='it_manager_approval'
            )
        self.write({'state': 'it_manager_approval'})

    def action_assign(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'assigned',
                notes=_('Assigned to IT Staff'),
                old_state='it_manager_approval', new_state='assigned'
            )
        self.write({'state': 'assigned'})

    def action_start_progress(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'in_progress',
                notes=_('Implementation started'),
                old_state='assigned', new_state='in_progress'
            )
        self.write({'state': 'in_progress'})

    def action_complete(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'completed',
                notes=_('Permission request completed'),
                old_state='in_progress', new_state='completed'
            )
        self.write({'state': 'completed'})

    def action_reject(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'rejected',
                notes=_('Permission request rejected'),
                old_state=self.state, new_state='rejected'
            )
        self.write({'state': 'rejected'})

    @api.model
    def default_get(self, fields_list):
        """Set default employee to current user's employee record"""
        res = super(BSSICPermissionRequest, self).default_get(fields_list)

        # Get current user's employee record
        current_user = self.env.user
        employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

        if employee:
            res['employee_id'] = employee.id
            # Also set employee_number if available
            if employee.int_id:
                res['employee_number'] = employee.int_id

        return res

    @api.onchange('employee_number')
    def _onchange_employee_number(self):
        """Handle employee number change"""
        # Check if user is a manager
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        current_user = self.env.user

        if self.employee_number:
            # Try with int_id field
            employee = self.env['hr.employee'].search([('int_id', '=', self.employee_number)], limit=1)

            if employee:
                # If user is a manager, check if the employee is in their department and reports to them
                if is_manager:
                    manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                    # Check if employee is the manager or reports to the manager in the same department
                    if (employee.id == manager_employee.id or
                        (employee.department_id.id == manager_employee.department_id.id and
                         employee.parent_id.id == manager_employee.id)):
                        self.employee_id = employee.id
                    else:
                        # Reset to manager's employee number
                        if manager_employee.int_id:
                            self.employee_number = manager_employee.int_id
                        self.employee_id = manager_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only select employees in your department who report to you.')
                        }}
                # If not a manager, only allow selecting themselves
                else:
                    user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                    if employee.id == user_employee.id:
                        self.employee_id = employee.id
                    else:
                        # Reset to user's employee number
                        if user_employee.int_id:
                            self.employee_number = user_employee.int_id
                        self.employee_id = user_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only create requests for yourself.')
                        }}
            else:
                # Clear the employee_id if no matching employee is found
                self.employee_id = False
                return {'warning': {
                    'title': _('Warning'),
                    'message': _('No employee found with this employee number.')
                }}

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        """Check permissions when employee_id is changed directly"""
        # Check if user is a manager
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        current_user = self.env.user

        if self.employee_id:
            # If user is a manager, check if the employee is in their department and reports to them
            if is_manager:
                manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                # Check if employee is the manager or reports to the manager in the same department
                if (self.employee_id.id == manager_employee.id or
                    (self.employee_id.department_id.id == manager_employee.department_id.id and
                     self.employee_id.parent_id.id == manager_employee.id)):
                    # Valid selection, update employee_number
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    # Invalid selection, reset to manager's employee
                    self.employee_id = manager_employee
                    if manager_employee.int_id:
                        self.employee_number = manager_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only select employees in your department who report to you.')
                    }}
            # If not a manager, only allow selecting themselves
            else:
                user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                if self.employee_id.id == user_employee.id:
                    # Valid selection, update employee_number
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    # Invalid selection, reset to current user's employee
                    self.employee_id = user_employee
                    if user_employee.int_id:
                        self.employee_number = user_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only create requests for yourself.')
                    }}
