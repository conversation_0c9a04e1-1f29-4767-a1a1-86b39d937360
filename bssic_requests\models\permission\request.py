from odoo import models, fields, api, _


class BSSICPermissionRequest(models.Model):
    _name = 'bssic.permission.request'
    _description = 'BSIC Permission Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    # Basic Information
    name = fields.Char('Request Reference', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                   tracking=True, ondelete="restrict")
    department_id = fields.Many2one(related='employee_id.department_id',
                                   string='Department', store=True, readonly=True)
    job_id = fields.Many2one(related='employee_id.job_id', string='Job Position',
                            store=True, readonly=True)
    request_date = fields.Date('Request Date', default=fields.Date.context_today,
                              required=True, tracking=True)
    description = fields.Text('Description', tracking=True)

    # State
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager_approval', 'Direct Manager Approval'),
        ('dept_manager_approval', 'Department Manager Approval'),
        ('it_manager_approval', 'IT Manager Approval'),
        ('assigned', 'Assigned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    # Activity Log
    activity_log_ids = fields.One2many('bssic.request.activity.log', 'permission_request_id', string='Activity Log')

    # Permission Details
    permission_type = fields.Selection([
        ('add', 'Add'),
        ('modify', 'Modify'),
        ('delete', 'Delete'),
        ('withdraw', 'Withdraw'),
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
    ], string='Permission Type', tracking=True)
    user_name = fields.Char('User Name', tracking=True)
    validity_from = fields.Date('Valid From', tracking=True)
    validity_to = fields.Date('Valid To', tracking=True)

    @api.model
    def create(self, vals):
        # Set sequence number
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('bssic.permission.request') or _('New')
        return super(BSSICPermissionRequest, self).create(vals)

    def action_submit(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'submitted',
                notes=_('Permission request submitted for approval'),
                old_state='draft', new_state='submitted'
            )
        self.write({'state': 'submitted'})

    def action_direct_manager_approve(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'direct_manager_approved',
                notes=_('Approved by Direct Manager'),
                old_state='submitted', new_state='direct_manager_approval'
            )
        self.write({'state': 'direct_manager_approval'})

    def action_dept_manager_approve(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'direct_manager_approved',
                notes=_('Approved by Department Manager'),
                old_state='direct_manager_approval', new_state='dept_manager_approval'
            )
        self.write({'state': 'dept_manager_approval'})
    
    def action_it_manager_approve(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'it_manager_approved',
                notes=_('Approved by IT Manager'),
                old_state='dept_manager_approval', new_state='it_manager_approval'
            )
        self.write({'state': 'it_manager_approval'})
    
    def action_assign(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'assigned',
                notes=_('Assigned to IT Staff'),
                old_state='it_manager_approval', new_state='assigned'
            )
        self.write({'state': 'assigned'})
    
    def action_start_progress(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'in_progress',
                notes=_('Implementation started'),
                old_state='assigned', new_state='in_progress'
            )
        self.write({'state': 'in_progress'})
    
    def action_complete(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'completed',
                notes=_('Permission request completed'),
                old_state='in_progress', new_state='completed'
            )
        self.write({'state': 'completed'})
    
    def action_reject(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.permission.request', self.id, 'rejected',
                notes=_('Permission request rejected'),
                old_state=self.state, new_state='rejected'
            )
        self.write({'state': 'rejected'})
