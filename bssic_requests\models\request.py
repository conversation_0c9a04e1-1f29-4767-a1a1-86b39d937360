from odoo import models, fields, api, _
from odoo.exceptions import UserError
from lxml import etree
from odoo.exceptions import ValidationError

class BSSICRequest(models.Model):
    _name = 'bssic.request'
    _description = 'BSSIC Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    name = fields.Char('Request Reference', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))
    # حقل الموظف - يمكن للمدير تعديله
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                   tracking=True, ondelete="restrict")
    # حقل رقم الموظف - يمكن للمدير تعديله
    employee_number = fields.Char(string='Employee Number (ID)', tracking=True,
                                help="Enter employee ID number to automatically fetch employee details")
    # حقول القسم والوظيفة هي بالفعل للقراءة فقط (readonly=True)
    department_id = fields.Many2one(related='employee_id.department_id',
                                   string='Department', store=True, readonly=True)
    job_id = fields.Many2one(related='employee_id.job_id', string='Job Position',
                            store=True, readonly=True)
    request_type_id = fields.Many2one('bssic.request.type', string='Request Type',
                                     required=True, tracking=True)
    request_type_code = fields.Char('Request Type Code', tracking=True)
    request_date = fields.Date('Request Date', default=fields.Date.context_today,
                              required=True, tracking=True)
    description = fields.Text('Description', tracking=True)

    # For password reset request
    username = fields.Char('Username', tracking=True)
    device_type = fields.Selection([
        ('internet', 'Internet'),
        ('system', 'System'),
        ('swift', 'Swift'),
        ('other', 'Other')
    ], string='Device Type', tracking=True)
    request_reason = fields.Selection([
        ('password_reset', 'Password Reset (Forgotten/Unable to login)'),
        ('account_reactivation', 'Account Reactivation (Account/Device locked)')
    ], string='Request Reason', tracking=True)

    # For USB request
    usb_purpose = fields.Char('Purpose of USB Usage', tracking=True)
    usb_duration = fields.Char('Required Duration', tracking=True)
    data_type = fields.Char('Type of Data to Transfer', tracking=True)

    # For device extension request
    extension_duration = fields.Char('Required Extension Period', tracking=True)
    extension_reason = fields.Text('Reason for Extension', tracking=True)

    # For permission request
    permission_type = fields.Selection([
        ('add', 'Add'),
        ('modify', 'Modify'),
        ('delete', 'Delete'),
        ('withdraw', 'Withdraw'),
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
    ], string='Permission Type', tracking=True)
    user_name = fields.Char('User Name', tracking=True)
    validity_from = fields.Date('Valid From', tracking=True)
    validity_to = fields.Date('Valid To', tracking=True)

    # Department permissions
    accounting_dept = fields.Boolean(string='Accounting Department', tracking=True)
    accounting_level = fields.Char(string='Accounting Level', tracking=True)
    accounting_group = fields.Char(string='Accounting Group', tracking=True)

    internal_audit = fields.Boolean(string='Internal Auditing', tracking=True)
    internal_audit_level = fields.Char(string='Internal Audit Level', tracking=True)
    internal_audit_group = fields.Char(string='Internal Audit Group', tracking=True)

    risk_dept = fields.Boolean(string='Risk', tracking=True)
    risk_level = fields.Char(string='Risk Level', tracking=True)
    risk_group = fields.Char(string='Risk Group', tracking=True)

    back_office_credits = fields.Boolean(string='Back Office - Credits', tracking=True)
    back_office_credits_level = fields.Char(string='Credits Level', tracking=True)
    back_office_credits_group = fields.Char(string='Credits Group', tracking=True)

    back_office_deposits = fields.Boolean(string='Back Office - Deposits', tracking=True)
    back_office_deposits_level = fields.Char(string='Deposits Level', tracking=True)
    back_office_deposits_group = fields.Char(string='Deposits Group', tracking=True)

    operations_dept = fields.Boolean(string='Operations Department', tracking=True)
    operations_level = fields.Char(string='Operations Level', tracking=True)
    operations_group = fields.Char(string='Operations Group', tracking=True)

    forex_exchange = fields.Boolean(string='Forex Exchange', tracking=True)
    forex_level = fields.Char(string='Forex Level', tracking=True)
    forex_group = fields.Char(string='Forex Group', tracking=True)

    banking_operations = fields.Boolean(string='Banking Operations', tracking=True)
    banking_level = fields.Char(string='Banking Level', tracking=True)
    banking_group = fields.Char(string='Banking Group', tracking=True)

    personnel_admin = fields.Boolean(string='Personnel & Admin', tracking=True)
    personnel_level = fields.Char(string='Personnel Level', tracking=True)
    personnel_group = fields.Char(string='Personnel Group', tracking=True)

    swift = fields.Boolean(string='Swift', tracking=True)
    swift_level = fields.Char(string='Swift Level', tracking=True)
    swift_group = fields.Char(string='Swift Group', tracking=True)

    # Transaction limits
    transaction_amount_limit = fields.Float(string='Transaction Amount Limit', tracking=True)
    auth_limit = fields.Float(string='Auth. O.D. Limit', tracking=True)
    max_amount_limit = fields.Float(string='MAX Amount Limit', tracking=True)

    # User signature
    user_signature = fields.Binary(string='User Signature', tracking=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager', 'Direct Manager Approval'),
        ('audit_manager', 'Audit Manager Approval'),
        ('it_manager', 'IT Manager Approval'),
        ('assigned', 'Assigned to IT Staff'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    # Activity Log
    activity_log_ids = fields.One2many('bssic.request.activity.log', 'request_id', string='Activity Log')

    # Modify the assigned_to field to filter only IT Staff users
    # Modify the assigned_to field definition to include a domain directly
    assigned_to = fields.Many2one('hr.employee', string='Assigned To',
                                 tracking=True, ondelete="set null",
                                 domain="[('department_id.name', 'ilike', 'IT')]")

    # Improve the _get_it_staff_domain method
    def _get_it_staff_domain(self):
        # Get the IT Staff group ID
        it_staff_group_id = self.env.ref('bssic_requests.group_bssic_it_staff').id

        # Find users who belong to the IT Staff group
        it_staff_users = self.env['res.users'].search([('groups_id', 'in', [it_staff_group_id])])

        # Find employees linked to these users
        it_staff_employees = self.env['hr.employee'].search([('user_id', 'in', it_staff_users.ids)])

        # Also include employees from IT department
        it_dept_employees = self.env['hr.employee'].search([('department_id.name', 'ilike', 'IT')])

        # Combine both sets of employees
        all_it_employees = it_staff_employees | it_dept_employees

        return [('id', 'in', all_it_employees.ids)]

    # Override the fields_view_get method to apply the domain dynamically
    @api.model
    def fields_view_get(self, view_id=None, view_type='form', toolbar=False, submenu=False):
        res = super(BSSICRequest, self).fields_view_get(view_id=view_id, view_type=view_type, toolbar=toolbar, submenu=submenu)
        if view_type == 'form':
            doc = etree.XML(res['arch'])

            # Set domain for assigned_to field
            for node in doc.xpath("//field[@name='assigned_to']"):
                node.set('domain', str(self._get_it_staff_domain()))

            # Check if user is a manager
            is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')

            # Set domain for employee_id field based on manager permissions
            employee_domain = self._get_employee_domain()
            # Get the list of employee IDs that match the domain
            employees = self.env['hr.employee'].search(employee_domain)

            # Set domain for employee_id field
            for node in doc.xpath("//field[@name='employee_id']"):
                # Set domain based on user permissions
                if is_manager:
                    # Managers can see employees in their department who report to them
                    node.set('domain', str([('id', 'in', employees.ids)]))
                else:
                    # Regular employees can only see themselves
                    current_user = self.env.user
                    employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if employee:
                        node.set('domain', str([('id', '=', employee.id)]))
                    else:
                        node.set('domain', str([('id', '=', -1)]))

                # Make the field editable in draft state for all users
                attrs = {'readonly': [('state', '!=', 'draft')]}
                node.set('attrs', str(attrs))

            # Always make request_type_id readonly when a specific request type is selected
            context = self.env.context
            if context.get('default_request_type_code') or context.get('default_request_type_id'):
                for node in doc.xpath("//field[@name='request_type_id']"):
                    node.set('readonly', '1')
                    node.set('force_save', '1')

            res['arch'] = etree.tostring(doc, encoding='unicode')
        return res
    rejection_reason = fields.Text('Rejection Reason', tracking=True)
    completion_notes = fields.Text('Completion Notes', tracking=True)

    @api.model
    def create(self, vals):
        # If request_type_code is provided but request_type_id is not, set request_type_id
        if vals.get('request_type_code') and not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([('code', '=', vals.get('request_type_code'))], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        # Set sequence number
        if vals.get('name', _('New')) == _('New'):
            # Get the request type name if available
            request_type_name = ""
            if vals.get('request_type_id'):
                request_type = self.env['bssic.request.type'].browse(vals.get('request_type_id'))
                if request_type:
                    request_type_name = request_type.name

            # Generate sequence
            sequence = self.env['ir.sequence'].next_by_code('bssic.request') or _('New')

            # Set name with request type if available
            if request_type_name:
                vals['name'] = f"{request_type_name} - {sequence}"
            else:
                vals['name'] = sequence

        return super(BSSICRequest, self).create(vals)

    def action_submit(self):
        # Log activity
        self.env['bssic.request.activity.log'].create_activity_log(
            'bssic.request', self.id, 'submitted',
            notes=_('Request submitted for approval'),
            old_state='draft', new_state='direct_manager'
        )

        # First set state to submitted
        self.state = 'submitted'
        # Then move to direct manager approval
        self.state = 'direct_manager'
        # Notify direct manager
        direct_manager = self.employee_id.parent_id
        if direct_manager and direct_manager.user_id:
            self.message_subscribe(partner_ids=[direct_manager.user_id.partner_id.id])
            self.message_post(
                body=_('A new request has been submitted for your approval.'),
                partner_ids=[direct_manager.user_id.partner_id.id]
            )

    def action_approve_direct_manager(self):
        # Check if this is a technical request
        if self.is_technical or self.request_type_id.code == 'technical':
            # Log activity
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'direct_manager_approved',
                notes=_('Approved by Direct Manager (Technical Request - Skip Audit)'),
                old_state='direct_manager', new_state='it_manager'
            )

            # Skip audit manager approval for technical requests
            self.state = 'it_manager'
            # Find IT manager and notify
            it_manager_group = self.env.ref('bssic_requests.group_bssic_it_manager')
            it_manager_users = it_manager_group.users
            partner_ids = it_manager_users.mapped('partner_id.id')
            if partner_ids:
                self.message_subscribe(partner_ids=partner_ids)
                self.message_post(
                    body=_('This technical request has been approved by the direct manager and requires your approval.'),
                    partner_ids=partner_ids
                )
        else:
            # Log activity
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'direct_manager_approved',
                notes=_('Approved by Direct Manager'),
                old_state='direct_manager', new_state='audit_manager'
            )

            # Regular workflow for non-technical requests
            self.state = 'audit_manager'
            # Find audit manager and notify
            audit_group = self.env.ref('bssic_requests.group_bssic_audit_manager')
            audit_users = audit_group.users
            partner_ids = audit_users.mapped('partner_id.id')
            if partner_ids:
                self.message_subscribe(partner_ids=partner_ids)
                self.message_post(
                    body=_('This request has been approved by the direct manager and requires your approval.'),
                    partner_ids=partner_ids
                )

    def action_approve_audit_manager(self):
        # Log activity
        self.env['bssic.request.activity.log'].create_activity_log(
            'bssic.request', self.id, 'audit_manager_approved',
            notes=_('Approved by Audit Manager'),
            old_state='audit_manager', new_state='it_manager'
        )

        self.state = 'it_manager'
        # Find IT manager and notify
        it_manager_group = self.env.ref('bssic_requests.group_bssic_it_manager')
        it_manager_users = it_manager_group.users
        partner_ids = it_manager_users.mapped('partner_id.id')
        if partner_ids:
            self.message_subscribe(partner_ids=partner_ids)
            self.message_post(
                body=_('This request has been approved by the audit manager and requires your approval.'),
                partner_ids=partner_ids
            )

    def action_approve_it_manager(self):
        # Log activity
        self.env['bssic.request.activity.log'].create_activity_log(
            'bssic.request', self.id, 'it_manager_approved',
            notes=_('Approved by IT Manager'),
            old_state='it_manager', new_state='assigned'
        )

        self.state = 'assigned'

    def action_assign(self):
        if not self.assigned_to:
            raise UserError(_('Please select an IT staff member to assign this request to.'))

        # Log activity
        self.env['bssic.request.activity.log'].create_activity_log(
            'bssic.request', self.id, 'assigned',
            notes=_('Assigned to IT Staff: %s') % self.assigned_to.name,
            old_state='assigned', new_state='in_progress',
            assigned_to_id=self.assigned_to.id
        )

        self.state = 'in_progress'
        # Notify assigned staff
        if self.assigned_to.user_id:
            self.message_subscribe(partner_ids=[self.assigned_to.user_id.partner_id.id])
            self.message_post(
                body=_('This request has been assigned to you for implementation.'),
                partner_ids=[self.assigned_to.user_id.partner_id.id]
            )

    def action_complete(self):
        if not self.completion_notes:
            raise UserError(_('Please add completion notes before marking as completed.'))

        # Log activity
        self.env['bssic.request.activity.log'].create_activity_log(
            'bssic.request', self.id, 'completed',
            notes=_('Request completed. Notes: %s') % self.completion_notes,
            old_state='in_progress', new_state='completed'
        )

        self.state = 'completed'
        # Notify employee
        if self.employee_id.user_id:
            self.message_post(
                body=_('Your request has been completed. Notes: %s') % self.completion_notes,
                partner_ids=[self.employee_id.user_id.partner_id.id]
            )

    def action_reject(self):
        return {
            'name': _('Reject Request'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.request.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_request_id': self.id}
        }

    # Add these computed fields to the BSSICRequest model
    show_password_fields = fields.Boolean(
        string='Show Password Fields',
        compute='_compute_show_fields'
    )
    show_usb_fields = fields.Boolean(
        string='Show USB Fields',
        compute='_compute_show_fields'
    )
    show_extension_fields = fields.Boolean(
        string='Show Extension Fields',
        compute='_compute_show_fields'
    )
    # Make sure this computed field is added
    show_permission_fields = fields.Boolean(
        string='Show Permission Fields',
        compute='_compute_show_fields'
    )
    show_email_fields = fields.Boolean(
        string='Show Email Fields',
        compute='_compute_show_fields'
    )
    show_technical_fields = fields.Boolean(
        string='Show Technical Fields',
        compute='_compute_show_fields'
    )

    @api.depends('request_type_id')
    def _compute_show_fields(self):
        for record in self:
            record.show_password_fields = record.request_type_id.show_password_fields if record.request_type_id else False
            record.show_usb_fields = record.request_type_id.show_usb_fields if record.request_type_id else False
            record.show_extension_fields = record.request_type_id.show_extension_fields if record.request_type_id else False
            record.show_permission_fields = record.request_type_id.show_permission_fields if record.request_type_id else False
            record.show_email_fields = record.request_type_id.show_email_fields if record.request_type_id else False
            is_tech = record.request_type_id.code == 'technical' if record.request_type_id else False
            record.show_technical_fields = is_tech
            record.is_technical = is_tech

    @api.depends()
    def _compute_is_manager(self):
        """Check if the current user has direct manager permissions"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        for record in self:
            record.is_manager = is_manager

    # Add this method to your BSSICRequest class
    # Modify the _get_it_staff_domain method to correctly filter IT Staff users
    def _get_it_staff_domain(self):
        # Get the IT Staff group ID (119 as shown in your image)
        it_staff_group_id = self.env.ref('bssic_requests.group_bssic_it_staff').id

        # Find users who belong to the IT Staff group
        it_staff_users = self.env['res.users'].search([('groups_id', 'in', [it_staff_group_id])])

        # Find employees linked to these users
        it_staff_employees = self.env['hr.employee'].search([('user_id', 'in', it_staff_users.ids)])

        # Return domain to filter only these employees
        return [('id', 'in', it_staff_employees.ids)]

    # Add this method to be called when the form view is loaded
    @api.onchange('state')
    def _onchange_state(self):
        if self.state == 'assigned':
            # When the state changes to 'assigned', update the domain for assigned_to field
            return {'domain': {'assigned_to': self._get_it_staff_domain()}}

    @api.model
    def _search(self, args, offset=0, limit=None, order=None, count=False, access_rights_uid=None):
        # If we're searching for employees to assign and the context has 'it_staff_only'
        context = self._context or {}
        if self._name == 'hr.employee' and context.get('it_staff_only'):
            # Apply the IT staff domain filter
            args = args + self.env['bssic.request']._get_it_staff_domain()
        return super(BSSICRequest, self)._search(args, offset, limit, order, count, access_rights_uid)

    # For email request
    email_type = fields.Selection([
        ('new', 'New Email'),
        ('password_reset', 'Password Reset'),
        ('2fa_reset', 'Two-Factor Authentication Reset'),
    ], string='Email Request Type', tracking=True)
    email_reason = fields.Text(string='Request Reason', tracking=True)

    # Technical request fields
    is_technical = fields.Boolean(string='Is Technical Request', default=False)
    is_manager = fields.Boolean(string='Is Manager', compute='_compute_is_manager', store=False)
    request_nature = fields.Selection([
        ('technical', 'Technical'),
        ('administrative', 'Administrative'),
    ], string='Request Type', default='technical', tracking=True)
    technical_category_id = fields.Many2one('bssic.technical.category', string='Category', tracking=True)
    technical_subcategory_id = fields.Many2one('bssic.technical.category', string='Subcategory',
                                             domain="[('parent_id', '=', technical_category_id)]", tracking=True)
    priority = fields.Selection([
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Urgent'),
    ], string='Priority', default='1', tracking=True)
    # Add these fields to your BSSICRequest class
    email_agreement_text = fields.Html(string='Email Usage Agreement', readonly=True)
    email_agreement_accepted = fields.Boolean(string='I agree to the terms and conditions', default=False)

    # Add this method to set the default agreement text
    @api.onchange('email_type')
    def _onchange_email_type(self):
        if self.email_type == 'new':
            self.email_agreement_text = """
            <div style="direction: rtl; text-align: right;">
                <h3 style="text-align: center;">إقرار والتزام بضوابط استعمال البريد الإلكتروني</h3>

                <p>منظومة البريد الإلكتروني (bsicbank.com) لمصرف الساحل والصحراء للاستثمار والتجارة، تهدف الى إنجاز أعمال المصرف بجميع أشكالها في شكل توفير وسيلة التواصل بين إدارات المصرف فيما بينها من جانب، وكذلك تواصل بعض هذه الإدارات مع الجهات الاعتبارية الأخرى من جانب آخر وذلك وفق الصلاحيات الممنوحة وحسب التسلسل العملية الإدارية المعمول بها.</p>

                <h4 style="text-align: center;">وفق شروط وضوابط استخدام البريد الالكتروني التالية:</h4>

                <h4>المسؤولية ودواعي الاستعمال:</h4>
                <ol>
                    <li>البريد الإلكتروني الممنوح هو ملك لمصرف الساحل والصحراء، يخول صاحبه باستعماله بمفرده فيما استخدامه شؤون المصرف ذات العلاقة بوظيفته وأعمال إدارته أو إنجاز ما يطلب منه القيام به من عمل وعلى ذلك فإن جميع ما ينشأ أو ينظم من مراسلات ترجع ملكيتها للمصرف.</li>
                    <li>حساب البريد الممنوح للموظفين مخصص لهم أثناء تواجدهم للعمل بالإدارة العامة فقط، ويتم إيقاف حساب البريد في حالة إعارة الموظف إلى أحد الجهات التابعة الأخرى.</li>
                    <li>استعمال البريد الالكتروني يستوجب احترام التسلسل الإداري الوظيفي وان لا يتجاوز مستوى مديره المباشر الا بإذن مباشر منه وضمن رئاسته للبريد الالكتروني وبعلم جميع من يراسله.</li>
                    <li>عند التواصل مع مستخدم البريد الالكتروني يجب ان يكتب بأسلوب مهذب ومهني ومن شأن زملائه او الغير وان يتأكد من العنوان المرسل له قبل الإرسال حتى لا تصل المعلومات الى أشخاص لا علاقة لهم بموضوع المراسلة.</li>
                    <li>ان يقوم بالاطلاع على البريد بشكل مستمر ومنتظم ولا يعفى من المسؤولية عند التأخير في الإنجاز.</li>
                    <li>يتعهد بعد استعمال البريد في التواصل لأغراض شخصية بجميع أنواعها او تجارية، والا يقوم بإطلاع الغير على محتويات بريده والحفاظ على السرية التامة.</li>
                    <li>ألا يقوم بأعمال تؤدي الى ضرر الاخرين او انتحال صفة او شخصية غير التي يشغلها بالمصرف، قد تجر المصرف الى جهات قضائية، او تعويضات، او تشويه سمعة المصرف، او غيرها من الآثار المعنوية.</li>
                    <li>أن يقوم بالتأكد من الحصول على إذن من الإدارة المعنية بالمصرف قبل القيام بإرسال أي وثائق او ملفات قد ترجع ملكيتها لجهات أخرى حتى لا تقع على المصرف مخالفة حقوق النشر والملفية الخاصة للغير.</li>
                </ol>

                <!-- Add more sections from the images as needed -->

                <p style="text-align: center; margin-top: 20px;">أقر أني قد اطلعت على الشروط المذكورة أعلاه والتي على دراية تامة بكل ما ورد فيها.</p>
            </div>
            """

    @api.constrains('state', 'email_type', 'email_agreement_accepted')
    def _check_email_agreement(self):
        for record in self:
            if record.email_type == 'new' and not record.email_agreement_accepted and record.state != 'draft':
                raise ValidationError(_("يجب الموافقة على شروط استخدام البريد الإلكتروني للمتابعة في طلب البريد الإلكتروني الجديد."))

    # Add this method to fetch employee details based on employee number
    # Update the employee_number field definition
    employee_number = fields.Char(string='Employee Number (ID)', tracking=True,
                                 help="Enter employee ID number to automatically fetch employee details")

    @api.onchange('employee_number')
    def _onchange_employee_number(self):
        if self.employee_number:
            # Try different possible field names for employee number
            employee = False
            # First try with int_id
            employee = self.env['hr.employee'].search([('int_id', '=', self.employee_number)], limit=1)

            # If not found, try with identification_id which is standard in Odoo
            if not employee:
                employee = self.env['hr.employee'].search([('identification_id', '=', self.employee_number)], limit=1)

            if employee:
                self.employee_id = employee.id
                # No need to set department_id and job_id as they are related fields
            else:
                # Clear the employee_id if no matching employee is found
                self.employee_id = False
                return {'warning': {
                    'title': 'Warning',
                    'message': 'No employee found with this employee number.'
                }}

    # Add this method to set default employee based on current user
    @api.model
    def default_get(self, fields_list):
        res = super(BSSICRequest, self).default_get(fields_list)

        # Get current user's employee record
        current_user = self.env.user
        employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

        if employee:
            res['employee_id'] = employee.id
            # Also set employee_number if available
            if employee.int_id:
                res['employee_number'] = employee.int_id

        # Check if request_type_code is in context and set request_type_id accordingly
        context = self.env.context
        if context.get('default_request_type_code'):
            request_type_code = context.get('default_request_type_code')
            request_type = self.env['bssic.request.type'].search([('code', '=', request_type_code)], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id
                res['request_type_code'] = request_type_code

        # Set default name if provided in context
        if context.get('default_name'):
            res['name'] = context.get('default_name')

        return res

    # Add domain for employee selection to show only employees in the same department for managers
    @api.model
    def _get_employee_domain(self):
        # Get current user's employee record
        current_user = self.env.user
        employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

        # Check if user is a manager
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')

        if is_manager and employee:
            # Get all employees in the same department that report to this manager
            department_employees = self.env['hr.employee'].search([
                '|',
                ('id', '=', employee.id),  # Include the manager
                '&',
                ('department_id', '=', employee.department_id.id),  # Same department
                ('parent_id', '=', employee.id)  # Reports to this manager
            ])
            return [('id', 'in', department_employees.ids)]
        else:
            # Regular employee can only select themselves
            if employee:
                return [('id', '=', employee.id)]
            else:
                return [('id', '=', -1)]  # No employee should match this domain

    # Update the onchange method to respect permissions
    @api.onchange('employee_number')
    def _onchange_employee_number(self):
        # Check if user is a manager
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        current_user = self.env.user

        if self.employee_number:
            # Try with int_id field
            employee = self.env['hr.employee'].search([('int_id', '=', self.employee_number)], limit=1)

            if employee:
                # If user is a manager, check if the employee is in their department and reports to them
                if is_manager:
                    manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                    # Check if employee is the manager or reports to the manager in the same department
                    if (employee.id == manager_employee.id or
                        (employee.department_id.id == manager_employee.department_id.id and
                         employee.parent_id.id == manager_employee.id)):
                        self.employee_id = employee.id
                    else:
                        # Reset to manager's employee number
                        if manager_employee.int_id:
                            self.employee_number = manager_employee.int_id
                        self.employee_id = manager_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only select employees in your department who report to you.')
                        }}
                # If not a manager, only allow selecting themselves
                else:
                    user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                    if employee.id == user_employee.id:
                        self.employee_id = employee.id
                    else:
                        # Reset to user's employee number
                        if user_employee.int_id:
                            self.employee_number = user_employee.int_id
                        self.employee_id = user_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only create requests for yourself.')
                        }}
            else:
                # Clear the employee_id if no matching employee is found
                self.employee_id = False
                return {'warning': {
                    'title': _('Warning'),
                    'message': _('No employee found with this employee number.')
                }}

    @api.onchange('request_type_code')
    def _onchange_request_type_code(self):
        if self.request_type_code:
            request_type = self.env['bssic.request.type'].search([('code', '=', self.request_type_code)], limit=1)
            if request_type:
                self.request_type_id = request_type.id

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        # Check if user is a manager
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')

        if self.employee_id:
            # If user is a manager, check if the employee is in their department and reports to them
            if is_manager:
                current_user = self.env.user
                manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                # Check if employee is the manager or reports to the manager in the same department
                if (self.employee_id.id == manager_employee.id or
                    (self.employee_id.department_id.id == manager_employee.department_id.id and
                     self.employee_id.parent_id.id == manager_employee.id)):
                    # Valid selection, update related fields
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    # Invalid selection, show warning
                    self.employee_id = manager_employee
                    if manager_employee.int_id:
                        self.employee_number = manager_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only select employees in your department who report to you.')
                    }}
            # If not a manager, only allow selecting themselves
            else:
                current_user = self.env.user
                user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                if self.employee_id.id == user_employee.id:
                    # Valid selection
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    # Invalid selection, reset to current user
                    self.employee_id = user_employee
                    if user_employee.int_id:
                        self.employee_number = user_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only create requests for yourself.')
                    }}