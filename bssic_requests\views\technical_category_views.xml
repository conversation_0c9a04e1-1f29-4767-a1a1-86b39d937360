<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Technical Category Tree View -->
    <record id="view_bssic_technical_category_tree" model="ir.ui.view">
        <field name="name">bssic.technical.category.tree</field>
        <field name="model">bssic.technical.category</field>
        <field name="arch" type="xml">
            <tree string="Technical Categories">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="parent_id"/>
            </tree>
        </field>
    </record>
    
    <!-- Technical Category Form View -->
    <record id="view_bssic_technical_category_form" model="ir.ui.view">
        <field name="name">bssic.technical.category.form</field>
        <field name="model">bssic.technical.category</field>
        <field name="arch" type="xml">
            <form string="Technical Category">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="code"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="parent_id"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" placeholder="Add a description..."/>
                        </page>
                        <page string="Child Categories" attrs="{'invisible': [('child_ids', '=', [])]}">
                            <field name="child_ids">
                                <tree>
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="code"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Technical Category Search View -->
    <record id="view_bssic_technical_category_search" model="ir.ui.view">
        <field name="name">bssic.technical.category.search</field>
        <field name="model">bssic.technical.category</field>
        <field name="arch" type="xml">
            <search string="Search Technical Categories">
                <field name="name"/>
                <field name="code"/>
                <field name="parent_id"/>
                <filter string="Top Level Categories" name="parent_is_false" domain="[('parent_id', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Parent Category" name="group_by_parent" context="{'group_by': 'parent_id'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- Technical Category Action -->
    <record id="action_bssic_technical_category" model="ir.actions.act_window">
        <field name="name">Technical Categories</field>
        <field name="res_model">bssic.technical.category</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new technical category
            </p>
        </field>
    </record>
    
    <!-- Technical Category Menu -->
    <menuitem id="menu_bssic_technical_category"
              name="Technical Categories"
              parent="menu_bssic_configuration"
              action="action_bssic_technical_category"
              sequence="20"/>
</odoo>
