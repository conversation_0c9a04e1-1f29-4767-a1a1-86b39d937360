from odoo import models, fields, api, _
from odoo.exceptions import UserError
from lxml import etree

class StationeryItem(models.Model):
    _name = 'bssic.stationery.item'
    _description = 'Stationery Item'
    _order = 'sequence, id'

    name = fields.Char('Item Name', required=True)
    sequence = fields.Integer('Sequence', default=10)
    active = fields.Boolean('Active', default=True)

class StationeryRequest(models.Model):
    _name = 'bssic.stationery.request'
    _description = 'Stationery Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    @api.model
    def fields_view_get(self, view_id=None, view_type='form', toolbar=False, submenu=False):
        """Override to dynamically set readonly attribute for approved_quantity field and employee_id domain"""
        res = super(StationeryRequest, self).fields_view_get(view_id=view_id, view_type=view_type, toolbar=toolbar, submenu=submenu)

        # Only modify form view
        if view_type == 'form':
            doc = etree.XML(res['arch'])

            # Find the approved_quantity field in the line_ids tree view
            for node in doc.xpath("//field[@name='line_ids']//field[@name='approved_quantity']"):
                # Check if user is HR manager
                is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
                if not is_hr_manager:
                    # If not HR manager, make the field readonly
                    node.set('readonly', '1')
                else:
                    # For HR managers, set a domain to make it editable only in hr_approval state
                    node.set('attrs', "{'readonly': [('parent.state', '!=', 'hr_approval')]}")

            # Set domain for employee_id field
            for node in doc.xpath("//field[@name='employee_id']"):
                # Check if user is a manager
                is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
                current_user = self.env.user

                if is_manager:
                    # Get manager's employee record
                    manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if manager_employee:
                        # Get all employees in the same department that report to this manager
                        department_employees = self.env['hr.employee'].search([
                            '|',
                            ('id', '=', manager_employee.id),  # Include the manager
                            '&',
                            ('department_id', '=', manager_employee.department_id.id),  # Same department
                            ('parent_id', '=', manager_employee.id)  # Reports to this manager
                        ])
                        node.set('domain', str([('id', 'in', department_employees.ids)]))
                else:
                    # Regular employee can only select themselves
                    employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if employee:
                        node.set('domain', str([('id', '=', employee.id)]))
                    else:
                        node.set('domain', str([('id', '=', -1)]))  # No employee should match this domain

            res['arch'] = etree.tostring(doc, encoding='unicode')
        return res

    name = fields.Char('Request Reference', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                   tracking=True, ondelete="restrict")
    employee_number = fields.Char(string='Employee Number (ID)', tracking=True,
                                help="Enter employee ID number to automatically fetch employee details")
    department_id = fields.Many2one(related='employee_id.department_id',
                                   string='Department', store=True, readonly=True)
    job_id = fields.Many2one(related='employee_id.job_id', string='Job Position',
                            store=True, readonly=True)
    request_date = fields.Date('Request Date', default=fields.Date.context_today,
                              required=True, tracking=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager', 'Direct Manager Approval'),
        ('warehouse_approval', 'Warehouse Manager Approval'),
        ('hr_approval', 'HR Approval'),
        ('pending_receipt', 'Pending Receipt Confirmation'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    # Stationery request lines
    line_ids = fields.One2many('bssic.stationery.request.line', 'request_id', string='Stationery Items')

    # Approval fields
    direct_manager_id = fields.Many2one('hr.employee', string='Direct Manager',
                                       related='employee_id.parent_id', store=True, readonly=True)
    warehouse_manager_id = fields.Many2one('hr.employee', string='Warehouse Manager', tracking=True)
    hr_manager_id = fields.Many2one('hr.employee', string='HR Manager', tracking=True)
    rejection_reason = fields.Text('Rejection Reason', tracking=True)
    notes = fields.Text('Notes', tracking=True)

    # Approval logs
    submission_user_id = fields.Many2one('res.users', string='Submitted by', readonly=True, tracking=True)
    submission_date = fields.Datetime(string='Submission Date', readonly=True, tracking=True)
    direct_manager_approval_user_id = fields.Many2one('res.users', string='Approved by (Manager)', readonly=True, tracking=True)
    direct_manager_approval_date = fields.Datetime(string='Manager Approval Date', readonly=True, tracking=True)
    warehouse_approval_user_id = fields.Many2one('res.users', string='Approved by (Warehouse)', readonly=True, tracking=True)
    warehouse_approval_date = fields.Datetime(string='Warehouse Approval Date', readonly=True, tracking=True)
    hr_approval_user_id = fields.Many2one('res.users', string='Approved by (HR)', readonly=True, tracking=True)
    hr_approval_date = fields.Datetime(string='HR Approval Date', readonly=True, tracking=True)
    receipt_confirmation_user_id = fields.Many2one('res.users', string='Receipt Confirmed by', readonly=True, tracking=True)
    receipt_confirmation_date = fields.Datetime(string='Receipt Confirmation Date', readonly=True, tracking=True)
    receipt_notes = fields.Text('Receipt Notes', tracking=True)
    rejection_user_id = fields.Many2one('res.users', string='Rejected by', readonly=True, tracking=True)
    rejection_date = fields.Datetime(string='Rejection Date', readonly=True, tracking=True)

    is_manager = fields.Boolean(string='Is Manager', compute='_compute_is_manager', store=False)
    is_hr_manager = fields.Boolean(string='Is HR Manager', compute='_compute_is_hr_manager', store=False)
    is_warehouse_manager = fields.Boolean(string='Is Warehouse Manager', compute='_compute_is_warehouse_manager', store=False)

    @api.constrains('line_ids')
    def _check_line_ids(self):
        """Check that the request has at least one stationery item when saving"""
        for record in self:
            if record.state != 'draft' and not record.line_ids:
                raise UserError(_('You cannot save a request without any stationery items.'))

    @api.depends()
    def _compute_is_manager(self):
        """Check if the current user has direct manager permissions"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        for record in self:
            record.is_manager = is_manager

    @api.depends()
    def _compute_is_hr_manager(self):
        """Check if the current user has HR manager permissions"""
        is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
        for record in self:
            record.is_hr_manager = is_hr_manager

    @api.depends()
    def _compute_is_warehouse_manager(self):
        """Check if the current user has Warehouse manager permissions"""
        is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')
        for record in self:
            record.is_warehouse_manager = is_warehouse_manager

    @api.model
    def default_get(self, fields_list):
        """Set default employee to current user's employee record"""
        res = super(StationeryRequest, self).default_get(fields_list)

        # Get current user's employee record
        current_user = self.env.user
        employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

        if employee:
            res['employee_id'] = employee.id
            # Also set employee_number if available
            if employee.int_id:
                res['employee_number'] = employee.int_id

        return res

    @api.model
    def create(self, vals):
        # Set sequence number
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('bssic.stationery.request') or _('New')
        return super(StationeryRequest, self).create(vals)

    def action_submit(self):
        # Check if there are any stationery items selected
        if not self.line_ids:
            raise UserError(_('You cannot submit an empty request. Please select at least one stationery item.'))

        # Record submission information
        self.submission_user_id = self.env.user.id
        self.submission_date = fields.Datetime.now()

        # First set state to submitted
        self.state = 'submitted'
        # Then move to direct manager approval
        self.state = 'direct_manager'
        # Notify direct manager
        direct_manager = self.employee_id.parent_id
        if direct_manager and direct_manager.user_id:
            self.message_subscribe(partner_ids=[direct_manager.user_id.partner_id.id])
            self.message_post(
                body=_('A new stationery request has been submitted for your approval.'),
                partner_ids=[direct_manager.user_id.partner_id.id]
            )

    def action_approve_direct_manager(self):
        # Record approval information
        self.direct_manager_approval_user_id = self.env.user.id
        self.direct_manager_approval_date = fields.Datetime.now()

        # Update state to warehouse approval
        self.state = 'warehouse_approval'

        # Find Warehouse manager and notify
        warehouse_group = self.env.ref('bssic_requests.group_bssic_warehouse_manager')
        warehouse_users = warehouse_group.users
        partner_ids = warehouse_users.mapped('partner_id.id')
        if partner_ids:
            self.message_subscribe(partner_ids=partner_ids)
            self.message_post(
                body=_('This stationery request has been approved by the direct manager and requires warehouse verification.'),
                partner_ids=partner_ids
            )

    def action_approve_warehouse(self):
        # Check if all items have stock quantities specified
        for line in self.line_ids:
            if line.stock_quantity < 0:
                raise UserError(_('You must specify the stock quantity for all items before approving the request. Item "%s" has no stock quantity.') % line.item_id.name)

        # Record approval information
        self.warehouse_approval_user_id = self.env.user.id
        self.warehouse_approval_date = fields.Datetime.now()

        # Update state to HR approval
        self.state = 'hr_approval'

        # Find HR manager and notify
        hr_group = self.env.ref('bssic_requests.group_bssic_hr_manager')
        hr_users = hr_group.users
        partner_ids = hr_users.mapped('partner_id.id')
        if partner_ids:
            self.message_subscribe(partner_ids=partner_ids)
            self.message_post(
                body=_('This stationery request has been verified by the warehouse manager and requires your approval.'),
                partner_ids=partner_ids
            )

    def action_approve_hr(self):
        # Check if all items have approved quantities specified
        for line in self.line_ids:
            if line.approved_quantity < 0:
                raise UserError(_('Approved quantity cannot be negative. Item "%s" has a negative approved quantity.') % line.item_id.name)

        # Record approval information
        self.hr_approval_user_id = self.env.user.id
        self.hr_approval_date = fields.Datetime.now()

        # Update state to pending receipt confirmation
        self.state = 'pending_receipt'

        # Notify employee to confirm receipt
        if self.employee_id.user_id:
            self.message_subscribe(partner_ids=[self.employee_id.user_id.partner_id.id])
            self.message_post(
                body=_('Your stationery request has been approved by HR. Please confirm receipt of the items once you receive them.'),
                partner_ids=[self.employee_id.user_id.partner_id.id]
            )

    def action_confirm_receipt_wizard(self):
        """Open the receipt confirmation wizard"""
        return {
            'name': _('Confirm Receipt'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.receipt.confirmation.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_stationery_request_id': self.id}
        }

    def action_confirm_receipt(self):
        """Confirm receipt of stationery items"""
        # Record receipt confirmation information
        self.receipt_confirmation_user_id = self.env.user.id
        self.receipt_confirmation_date = fields.Datetime.now()

        # Update state to completed
        self.state = 'completed'

        # Notify HR and direct manager
        partner_ids = []
        if self.direct_manager_approval_user_id:
            partner_ids.append(self.direct_manager_approval_user_id.partner_id.id)
        if self.hr_approval_user_id:
            partner_ids.append(self.hr_approval_user_id.partner_id.id)

        if partner_ids:
            message_body = _('The employee has confirmed receipt of the stationery items.')
            if self.receipt_notes:
                message_body += _(' Notes: %s') % self.receipt_notes

            self.message_post(
                body=message_body,
                partner_ids=partner_ids
            )

    def action_reject(self):
        return {
            'name': _('Reject Request'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.request.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_request_id': self.id, 'default_model': 'bssic.stationery.request'}
        }

    # Add this method to fetch employee details based on employee number
    @api.onchange('employee_number')
    def _onchange_employee_number(self):
        # Check if user is a manager
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        current_user = self.env.user

        if self.employee_number:
            # Try with int_id field
            employee = self.env['hr.employee'].search([('int_id', '=', self.employee_number)], limit=1)

            if employee:
                # If user is a manager, check if the employee is in their department and reports to them
                if is_manager:
                    manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                    # Check if employee is the manager or reports to the manager in the same department
                    if (employee.id == manager_employee.id or
                        (employee.department_id.id == manager_employee.department_id.id and
                         employee.parent_id.id == manager_employee.id)):
                        self.employee_id = employee.id
                    else:
                        # Reset to manager's employee number
                        if manager_employee.int_id:
                            self.employee_number = manager_employee.int_id
                        self.employee_id = manager_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only select employees in your department who report to you.')
                        }}
                # If not a manager, only allow selecting themselves
                else:
                    user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                    if employee.id == user_employee.id:
                        self.employee_id = employee.id
                    else:
                        # Reset to user's employee number
                        if user_employee.int_id:
                            self.employee_number = user_employee.int_id
                        self.employee_id = user_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only create requests for yourself.')
                        }}
            else:
                # Clear the employee_id if no matching employee is found
                self.employee_id = False
                return {'warning': {
                    'title': _('Warning'),
                    'message': _('No employee found with this employee number.')
                }}

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        """Check permissions when employee_id is changed directly"""
        # Check if user is a manager
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        current_user = self.env.user

        if self.employee_id:
            # If user is a manager, check if the employee is in their department and reports to them
            if is_manager:
                manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                # Check if employee is the manager or reports to the manager in the same department
                if (self.employee_id.id == manager_employee.id or
                    (self.employee_id.department_id.id == manager_employee.department_id.id and
                     self.employee_id.parent_id.id == manager_employee.id)):
                    # Valid selection, update employee_number
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    # Invalid selection, reset to manager's employee
                    self.employee_id = manager_employee
                    if manager_employee.int_id:
                        self.employee_number = manager_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only select employees in your department who report to you.')
                    }}
            # If not a manager, only allow selecting themselves
            else:
                user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                if self.employee_id.id == user_employee.id:
                    # Valid selection, update employee_number
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    # Invalid selection, reset to current user's employee
                    self.employee_id = user_employee
                    if user_employee.int_id:
                        self.employee_number = user_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only create requests for yourself.')
                    }}

class StationeryRequestLine(models.Model):
    _name = 'bssic.stationery.request.line'
    _description = 'Stationery Request Line'

    request_id = fields.Many2one('bssic.stationery.request', string='Request', required=True, ondelete='cascade')
    item_id = fields.Many2one('bssic.stationery.item', string='Item', required=True)
    requested_quantity = fields.Float('Requested Quantity', default=1.0, required=True)
    stock_quantity = fields.Float('Stock Quantity', default=0.0)
    approved_quantity = fields.Float('Approved Quantity', default=0.0)
    can_edit_approved_quantity = fields.Boolean(compute='_compute_can_edit_approved_quantity')
    can_edit_stock_quantity = fields.Boolean(compute='_compute_can_edit_stock_quantity')

    @api.constrains('approved_quantity', 'request_id.state')
    def _check_approved_quantity(self):
        """Ensure approved quantity is not negative when HR manager approves the request"""
        for record in self:
            if record.request_id.state == 'pending_receipt' and record.approved_quantity < 0:
                raise UserError(_('Approved quantity cannot be negative when approving the request.'))

    @api.depends('request_id.state')
    def _compute_can_edit_approved_quantity(self):
        """Check if the current user can edit the approved quantity"""
        is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
        for record in self:
            record.can_edit_approved_quantity = is_hr_manager and record.request_id.state == 'hr_approval'

    @api.depends('request_id.state')
    def _compute_can_edit_stock_quantity(self):
        """Check if the current user can edit the stock quantity"""
        is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')
        for record in self:
            record.can_edit_stock_quantity = is_warehouse_manager and record.request_id.state == 'warehouse_approval'

    notes = fields.Char('Notes')

    def write(self, vals):
        """Override write to check permissions for approved_quantity and stock_quantity"""
        if 'approved_quantity' in vals:
            # Check if user is HR manager and request is in HR approval state
            is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
            if not is_hr_manager:
                # Remove approved_quantity from vals if user is not HR manager
                vals.pop('approved_quantity')
            elif self.request_id.state != 'hr_approval':
                # Remove approved_quantity from vals if request is not in HR approval state
                vals.pop('approved_quantity')

        if 'stock_quantity' in vals:
            # Check if user is Warehouse manager and request is in Warehouse approval state
            is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')
            if not is_warehouse_manager:
                # Remove stock_quantity from vals if user is not Warehouse manager
                vals.pop('stock_quantity')
            elif self.request_id.state != 'warehouse_approval':
                # Remove stock_quantity from vals if request is not in Warehouse approval state
                vals.pop('stock_quantity')

        return super(StationeryRequestLine, self).write(vals)
