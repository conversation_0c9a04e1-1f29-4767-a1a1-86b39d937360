from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime


class BSSICRequestActivityLog(models.Model):
    _name = 'bssic.request.activity.log'
    _description = 'BSIC Request Activity Log'
    _order = 'create_date desc'
    _rec_name = 'activity_type'

    # Relations
    request_id = fields.Many2one('bssic.request', string='Request', ondelete='cascade')
    permission_request_id = fields.Many2one('bssic.permission.request', string='Permission Request', ondelete='cascade')
    stationery_request_id = fields.Many2one('bssic.stationery.request', string='Stationery Request', ondelete='cascade')

    # Activity Details
    activity_type = fields.Selection([
        ('submitted', 'Submitted by'),
        ('direct_manager_approved', 'Approved by (Manager)'),
        ('audit_manager_approved', 'Approved by (Audit)'),
        ('it_manager_approved', 'Approved by (IT Manager)'),
        ('hr_approved', 'Approved by (HR)'),
        ('warehouse_approved', 'Approved by (Warehouse)'),
        ('assigned', 'Assigned to IT Staff'),
        ('in_progress', 'Started Implementation'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
        ('receipt_confirmed', 'Receipt Confirmed by'),
        ('cancelled', 'Cancelled'),
        ('modified', 'Modified'),
    ], string='Activity Type', required=True)

    activity_date = fields.Datetime(string='Activity Date', default=fields.Datetime.now, required=True)
    user_id = fields.Many2one('res.users', string='User', required=True, default=lambda self: self.env.user)
    employee_id = fields.Many2one('hr.employee', string='Employee', compute='_compute_employee_id', store=True)

    # Activity Details
    notes = fields.Text(string='Notes')
    old_state = fields.Char(string='Previous State')
    new_state = fields.Char(string='New State')

    # Additional fields for specific activities
    assigned_to_id = fields.Many2one('hr.employee', string='Assigned To')
    rejection_reason = fields.Text(string='Rejection Reason')

    @api.depends('user_id')
    def _compute_employee_id(self):
        for record in self:
            employee = self.env['hr.employee'].search([('user_id', '=', record.user_id.id)], limit=1)
            record.employee_id = employee.id if employee else False

    @api.constrains('request_id', 'permission_request_id', 'stationery_request_id')
    def _check_request_reference(self):
        for record in self:
            if not any([record.request_id, record.permission_request_id, record.stationery_request_id]):
                raise ValidationError(_('At least one request reference must be set.'))

    def name_get(self):
        result = []
        for record in self:
            activity_name = dict(record._fields['activity_type'].selection).get(record.activity_type, record.activity_type)
            name = f"{activity_name} - {record.activity_date.strftime('%Y-%m-%d %H:%M')}"
            result.append((record.id, name))
        return result

    @api.model
    def create_activity_log(self, request_model, request_id, activity_type, notes=None, old_state=None, new_state=None, assigned_to_id=None, rejection_reason=None):
        """
        Create an activity log entry

        :param request_model: Model name ('bssic.request', 'bssic.permission.request', etc.)
        :param request_id: ID of the request
        :param activity_type: Type of activity
        :param notes: Additional notes
        :param old_state: Previous state
        :param new_state: New state
        :param assigned_to_id: Employee assigned to (if applicable)
        :param rejection_reason: Reason for rejection (if applicable)
        """
        if not request_id:
            return False

        vals = {
            'activity_type': activity_type,
            'notes': notes,
            'old_state': old_state,
            'new_state': new_state,
            'assigned_to_id': assigned_to_id,
            'rejection_reason': rejection_reason,
        }

        # Set the appropriate request field based on model
        if request_model == 'bssic.request':
            vals['request_id'] = request_id
        elif request_model == 'bssic.permission.request':
            vals['permission_request_id'] = request_id
        elif request_model == 'bssic.stationery.request':
            vals['stationery_request_id'] = request_id
        else:
            # If model is not recognized, don't create the log
            return False

        return self.create(vals)

    def get_activity_display_name(self):
        """Get a user-friendly display name for the activity"""
        activity_names = {
            'submitted': _('Submitted by'),
            'direct_manager_approved': _('Approved by (Manager)'),
            'audit_manager_approved': _('Approved by (Audit)'),
            'it_manager_approved': _('Approved by (IT Manager)'),
            'hr_approved': _('Approved by (HR)'),
            'warehouse_approved': _('Approved by (Warehouse)'),
            'assigned': _('Assigned to IT Staff'),
            'in_progress': _('Started Implementation'),
            'completed': _('Completed'),
            'rejected': _('Rejected'),
            'receipt_confirmed': _('Receipt Confirmed by'),
            'cancelled': _('Cancelled'),
            'modified': _('Modified'),
        }
        return activity_names.get(self.activity_type, self.activity_type)

    def get_state_display_name(self, state):
        """Get a user-friendly display name for states"""
        state_names = {
            'draft': _('Draft'),
            'submitted': _('Submitted'),
            'direct_manager': _('Direct Manager Approval'),
            'direct_manager_approval': _('Direct Manager Approval'),
            'audit_manager': _('Audit Manager Approval'),
            'dept_manager_approval': _('Department Manager Approval'),
            'it_manager': _('IT Manager Approval'),
            'it_manager_approval': _('IT Manager Approval'),
            'hr_approval': _('HR Approval'),
            'warehouse_approval': _('Warehouse Approval'),
            'assigned': _('Assigned to IT Staff'),
            'in_progress': _('In Progress'),
            'completed': _('Completed'),
            'rejected': _('Rejected'),
            'cancelled': _('Cancelled'),
        }
        return state_names.get(state, state)
