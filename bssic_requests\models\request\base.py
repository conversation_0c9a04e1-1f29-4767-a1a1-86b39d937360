from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from lxml import etree


class BSSICRequest(models.Model):
    _name = 'bssic.request'
    _description = 'BSIC Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    # Basic Information
    name = fields.Char('Request Reference', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                   tracking=True, ondelete="restrict")
    employee_number = fields.Char(string='Employee Number (ID)', tracking=True,
                                help="Enter employee ID number to automatically fetch employee details")
    department_id = fields.Many2one(related='employee_id.department_id',
                                   string='Department', store=True, readonly=True)
    job_id = fields.Many2one(related='employee_id.job_id', string='Job Position',
                            store=True, readonly=True)
    request_type_id = fields.Many2one('bssic.request.type', string='Request Type',
                                     required=True, tracking=True)
    request_type_code = fields.Char('Request Type Code', tracking=True)
    request_date = fields.Date('Request Date', default=fields.Date.context_today,
                              required=True, tracking=True)
    description = fields.Text('Description', tracking=True)

    # State and Activity Log
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager', 'Direct Manager Approval'),
        ('audit_manager', 'Audit Manager Approval'),
        ('it_manager', 'IT Manager Approval'),
        ('assigned', 'Assigned to IT Staff'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)
    
    activity_log_ids = fields.One2many('bssic.request.activity.log', 'request_id', string='Activity Log')
    
    # Assignment and Notes
    assigned_to = fields.Many2one('hr.employee', string='Assigned To',
                                 tracking=True, ondelete="set null",
                                 domain="[('department_id.name', 'ilike', 'IT')]")
    rejection_reason = fields.Text('Rejection Reason', tracking=True)
    completion_notes = fields.Text('Completion Notes', tracking=True)

    # Password Reset Fields
    username = fields.Char('Username', tracking=True)
    device_type = fields.Selection([
        ('internet', 'Internet'),
        ('system', 'System'),
        ('swift', 'Swift'),
        ('other', 'Other')
    ], string='Device Type', tracking=True)
    request_reason = fields.Selection([
        ('password_reset', 'Password Reset (Forgotten/Unable to login)'),
        ('account_reactivation', 'Account Reactivation (Account/Device locked)')
    ], string='Request Reason', tracking=True)

    # USB Request Fields
    usb_purpose = fields.Char('Purpose of USB Usage', tracking=True)
    usb_duration = fields.Char('Required Duration', tracking=True)
    data_type = fields.Char('Type of Data to Transfer', tracking=True)

    # Device Extension Fields
    extension_duration = fields.Char('Required Extension Period', tracking=True)
    extension_reason = fields.Text('Reason for Extension', tracking=True)

    # Email Request Fields
    email_type = fields.Selection([
        ('new', 'New Email'),
        ('password_reset', 'Password Reset'),
        ('2fa_reset', 'Two-Factor Authentication Reset'),
    ], string='Email Request Type', tracking=True)
    email_reason = fields.Text(string='Request Reason', tracking=True)
    email_agreement_text = fields.Html(string='Email Usage Agreement', readonly=True)
    email_agreement_accepted = fields.Boolean(string='I agree to the terms and conditions', default=False)

    # Technical Request Fields
    is_technical = fields.Boolean(string='Is Technical Request', default=False)
    is_manager = fields.Boolean(string='Is Manager', compute='_compute_is_manager', store=False)
    request_nature = fields.Selection([
        ('technical', 'Technical'),
        ('administrative', 'Administrative'),
    ], string='Request Type', default='technical', tracking=True)
    technical_category_id = fields.Many2one('bssic.technical.category', string='Category', tracking=True)
    technical_subcategory_id = fields.Many2one('bssic.technical.category', string='Subcategory',
                                             domain="[('parent_id', '=', technical_category_id)]", tracking=True)
    priority = fields.Selection([
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Urgent'),
    ], string='Priority', default='1', tracking=True)

    # User signature
    user_signature = fields.Binary(string='User Signature', tracking=True)

    # Computed Fields for UI
    show_password_fields = fields.Boolean(string='Show Password Fields', compute='_compute_show_fields')
    show_usb_fields = fields.Boolean(string='Show USB Fields', compute='_compute_show_fields')
    show_extension_fields = fields.Boolean(string='Show Extension Fields', compute='_compute_show_fields')
    show_permission_fields = fields.Boolean(string='Show Permission Fields', compute='_compute_show_fields')
    show_email_fields = fields.Boolean(string='Show Email Fields', compute='_compute_show_fields')
    show_technical_fields = fields.Boolean(string='Show Technical Fields', compute='_compute_show_fields')

    @api.depends('request_type_id')
    def _compute_show_fields(self):
        for record in self:
            record.show_password_fields = record.request_type_id.show_password_fields if record.request_type_id else False
            record.show_usb_fields = record.request_type_id.show_usb_fields if record.request_type_id else False
            record.show_extension_fields = record.request_type_id.show_extension_fields if record.request_type_id else False
            record.show_permission_fields = record.request_type_id.show_permission_fields if record.request_type_id else False
            record.show_email_fields = record.request_type_id.show_email_fields if record.request_type_id else False
            is_tech = record.request_type_id.code == 'technical' if record.request_type_id else False
            record.show_technical_fields = is_tech
            record.is_technical = is_tech

    @api.depends()
    def _compute_is_manager(self):
        """Check if the current user has direct manager permissions"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        for record in self:
            record.is_manager = is_manager

    @api.model
    def create(self, vals):
        # If request_type_code is provided but request_type_id is not, set request_type_id
        if vals.get('request_type_code') and not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([('code', '=', vals.get('request_type_code'))], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        # Set sequence number
        if vals.get('name', _('New')) == _('New'):
            # Get the request type name if available
            request_type_name = ""
            if vals.get('request_type_id'):
                request_type = self.env['bssic.request.type'].browse(vals.get('request_type_id'))
                if request_type:
                    request_type_name = request_type.name

            # Generate sequence
            sequence = self.env['ir.sequence'].next_by_code('bssic.request') or _('New')

            # Set name with request type if available
            if request_type_name:
                vals['name'] = f"{request_type_name} - {sequence}"
            else:
                vals['name'] = sequence

        return super(BSSICRequest, self).create(vals)

    @api.onchange('email_type')
    def _onchange_email_type(self):
        if self.email_type == 'new':
            self.email_agreement_text = """
            <div style="direction: rtl; text-align: right;">
                <h3 style="text-align: center;">إقرار والتزام بضوابط استعمال البريد الإلكتروني</h3>
                <p>منظومة البريد الإلكتروني (bsicbank.com) لمصرف الساحل والصحراء للاستثمار والتجارة، تهدف الى إنجاز أعمال المصرف بجميع أشكالها في شكل توفير وسيلة التواصل بين إدارات المصرف فيما بينها من جانب، وكذلك تواصل بعض هذه الإدارات مع الجهات الاعتبارية الأخرى من جانب آخر وذلك وفق الصلاحيات الممنوحة وحسب التسلسل العملية الإدارية المعمول بها.</p>
                <h4 style="text-align: center;">وفق شروط وضوابط استخدام البريد الالكتروني التالية:</h4>
            </div>
            """
        else:
            self.email_agreement_text = False

    def action_reject(self):
        return {
            'name': _('Reject Request'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.request.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_request_id': self.id}
        }
