<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- قاعدة لعرض الطلبات الخاصة بالموظف فقط -->
    <record id="rule_bssic_request_employee" model="ir.rule">
        <field name="name">BSSIC Request: Employees see only their own requests</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- قاعدة للمدراء والمستخدمين ذوي الصلاحيات الخاصة لرؤية جميع الطلبات -->
    <record id="rule_bssic_request_manager" model="ir.rule">
        <field name="name">BSSIC Request: Managers see all requests</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[
            (4, ref('bssic_requests.group_bssic_direct_manager')),
            (4, ref('bssic_requests.group_bssic_audit_manager')),
            (4, ref('bssic_requests.group_bssic_it_manager')),
            (4, ref('bssic_requests.group_bssic_it_staff'))
        ]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>
</odoo>