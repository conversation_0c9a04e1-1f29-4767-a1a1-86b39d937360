<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Define module category first -->
    <record id="module_category_bssic_requests" model="ir.module.category">
        <field name="name">BSIC Requests</field>
        <field name="description">Category for BSIC Requests module</field>
        <field name="sequence">10</field>
    </record>

    <!-- Then define groups that use this category -->
    <record id="group_bssic_employee" model="res.groups">
        <field name="name">Employee</field>
        <field name="category_id" ref="module_category_bssic_requests"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <!-- Direct Manager Group -->
    <record id="group_bssic_direct_manager" model="res.groups">
        <field name="name">Direct Manager</field>
        <field name="category_id" ref="module_category_bssic_requests"/>
        <field name="implied_ids" eval="[(4, ref('group_bssic_employee'))]"/>
    </record>

    <!-- Audit Manager Group -->
    <record id="group_bssic_audit_manager" model="res.groups">
        <field name="name">Audit Manager</field>
        <field name="category_id" ref="module_category_bssic_requests"/>
        <field name="implied_ids" eval="[(4, ref('group_bssic_employee'))]"/>
    </record>

    <!-- IT Staff Group -->
    <record id="group_bssic_it_staff" model="res.groups">
        <field name="name">IT Staff</field>
        <field name="category_id" ref="module_category_bssic_requests"/>
        <field name="implied_ids" eval="[(4, ref('group_bssic_employee'))]"/>
    </record>

    <!-- IT Manager Group -->
    <record id="group_bssic_it_manager" model="res.groups">
        <field name="name">IT Manager</field>
        <field name="category_id" ref="module_category_bssic_requests"/>
        <field name="implied_ids" eval="[(4, ref('group_bssic_it_staff'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

    <!-- HR Manager Group -->
    <record id="group_bssic_hr_manager" model="res.groups">
        <field name="name">HR Manager</field>
        <field name="category_id" ref="module_category_bssic_requests"/>
        <field name="implied_ids" eval="[(4, ref('group_bssic_employee'))]"/>
    </record>

    <!-- Warehouse Manager Group -->
    <record id="group_bssic_warehouse_manager" model="res.groups">
        <field name="name">Warehouse Manager</field>
        <field name="category_id" ref="module_category_bssic_requests"/>
        <field name="implied_ids" eval="[(4, ref('group_bssic_employee'))]"/>
    </record>

    <!-- Record Rules -->
    <!-- Rule for employees to see only their own requests -->
    <record id="rule_bssic_request_employee" model="ir.rule">
        <field name="name">Employee: See Own Requests</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_bssic_employee'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- Rule for direct managers to see their team's requests -->
    <record id="rule_bssic_request_direct_manager" model="ir.rule">
        <field name="name">Direct Manager: See Team Requests</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), ('employee_id.parent_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_bssic_direct_manager'))]"/>
    </record>

    <!-- Rule for audit managers to see all requests -->
    <record id="rule_bssic_request_audit_manager" model="ir.rule">
        <field name="name">Audit Manager: See All Requests</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_bssic_audit_manager'))]"/>
    </record>

    <!-- Rule for IT managers to see all requests -->
    <record id="rule_bssic_request_it_manager" model="ir.rule">
        <field name="name">IT Manager: See All Requests</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_bssic_it_manager'))]"/>
    </record>

    <!-- Rule for IT staff to see assigned requests -->
    <record id="rule_bssic_request_it_staff" model="ir.rule">
        <field name="name">IT Staff: See Assigned Requests</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), ('assigned_to.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_bssic_it_staff'))]"/>
    </record>

    <!-- Stationery Request Rules -->
    <!-- Rule for employees to see only their own stationery requests -->
    <record id="rule_bssic_stationery_request_employee" model="ir.rule">
        <field name="name">Employee: See Own Stationery Requests</field>
        <field name="model_id" ref="model_bssic_stationery_request"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_bssic_employee'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- Rule for direct managers to see their team's stationery requests -->
    <record id="rule_bssic_stationery_request_direct_manager" model="ir.rule">
        <field name="name">Direct Manager: See Team Stationery Requests</field>
        <field name="model_id" ref="model_bssic_stationery_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), ('employee_id.parent_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_bssic_direct_manager'))]"/>
    </record>

    <!-- Rule for HR managers to see all stationery requests -->
    <record id="rule_bssic_stationery_request_hr_manager" model="ir.rule">
        <field name="name">HR Manager: See All Stationery Requests</field>
        <field name="model_id" ref="model_bssic_stationery_request"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_bssic_hr_manager'))]"/>
    </record>

    <!-- Rule for Warehouse managers to see all stationery requests -->
    <record id="rule_bssic_stationery_request_warehouse_manager" model="ir.rule">
        <field name="name">Warehouse Manager: See All Stationery Requests</field>
        <field name="model_id" ref="model_bssic_stationery_request"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_bssic_warehouse_manager'))]"/>
    </record>
</odoo>