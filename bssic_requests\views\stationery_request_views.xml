<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Stationery Item Views -->
    <record id="view_stationery_item_tree" model="ir.ui.view">
        <field name="name">bssic.stationery.item.tree</field>
        <field name="model">bssic.stationery.item</field>
        <field name="arch" type="xml">
            <tree string="Stationery Items">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="view_stationery_item_form" model="ir.ui.view">
        <field name="name">bssic.stationery.item.form</field>
        <field name="model">bssic.stationery.item</field>
        <field name="arch" type="xml">
            <form string="Stationery Item">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="sequence"/>
                        <field name="active"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>



    <!-- Stationery Request Views -->
    <record id="view_stationery_request_tree" model="ir.ui.view">
        <field name="name">bssic.stationery.request.tree</field>
        <field name="model">bssic.stationery.request</field>
        <field name="arch" type="xml">
            <tree string="Stationery Requests" decoration-info="state == 'draft'" decoration-warning="state == 'direct_manager' or state == 'hr_approval'" decoration-success="state == 'completed'" decoration-danger="state == 'rejected'">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="request_date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="view_stationery_request_form" model="ir.ui.view">
        <field name="name">bssic.stationery.request.form</field>
        <field name="model">bssic.stationery.request</field>
        <field name="arch" type="xml">
            <form string="Stationery Request">
                <header>
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"/>
                    <button name="action_approve_direct_manager" string="Approve (Manager)" type="object" class="oe_highlight" states="direct_manager" groups="bssic_requests.group_bssic_direct_manager"/>
                    <button name="action_approve_warehouse" string="Approve (Warehouse)" type="object" class="oe_highlight" states="warehouse_approval" groups="bssic_requests.group_bssic_warehouse_manager"/>
                    <button name="action_approve_hr" string="Approve (HR)" type="object" class="oe_highlight" states="hr_approval" groups="bssic_requests.group_bssic_hr_manager"/>
                    <button name="action_confirm_receipt_wizard" string="Confirm Receipt" type="object" class="oe_highlight" states="pending_receipt"/>
                    <button name="action_reject" string="Reject" type="object" class="btn-danger" states="direct_manager,warehouse_approval,hr_approval" groups="bssic_requests.group_bssic_direct_manager,bssic_requests.group_bssic_warehouse_manager,bssic_requests.group_bssic_hr_manager"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,direct_manager,warehouse_approval,hr_approval,pending_receipt,completed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="is_manager" invisible="1"/>
                            <field name="is_hr_manager" invisible="1"/>
                            <field name="is_warehouse_manager" invisible="1"/>
                            <field name="employee_number" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="employee_id" options="{'no_create': True}" attrs="{'readonly': [('state', '!=', 'draft')]}" />
                            <field name="department_id" readonly="1"/>
                            <field name="job_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="request_date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="rejection_reason" readonly="1" attrs="{'invisible': [('state', '!=', 'rejected')]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Stationery Items">
                            <field name="line_ids" attrs="{'readonly': ['|', '|', ('state', 'not in', ['draft', 'warehouse_approval', 'hr_approval']), '&amp;', ('state', '=', 'warehouse_approval'), ('is_warehouse_manager', '=', False), '&amp;', ('state', '=', 'hr_approval'), ('is_hr_manager', '=', False)]}" required="1">
                                <tree editable="bottom">
                                    <field name="item_id"/>
                                    <field name="requested_quantity"/>
                                    <field name="stock_quantity" attrs="{'readonly': ['|', ('parent.state', '!=', 'warehouse_approval'), ('parent.is_warehouse_manager', '=', False)], 'required': [('parent.state', '=', 'warehouse_approval'), ('parent.is_warehouse_manager', '=', True)]}"/>
                                    <field name="approved_quantity" attrs="{'readonly': ['|', ('parent.state', '!=', 'hr_approval'), ('parent.is_hr_manager', '=', False)]}" required="1"/>
                                    <field name="notes"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Notes">
                            <field name="notes" placeholder="Add any additional notes here..."/>
                        </page>
                        <page string="سجل الحركات">
                            <group>
                                <group string="معلومات التقديم">
                                    <field name="submission_user_id" readonly="1"/>
                                    <field name="submission_date" readonly="1"/>
                                </group>
                                <group string="موافقة المدير المباشر" attrs="{'invisible': [('direct_manager_approval_date', '=', False)]}">
                                    <field name="direct_manager_approval_user_id" readonly="1"/>
                                    <field name="direct_manager_approval_date" readonly="1"/>
                                </group>
                            </group>
                            <group>
                                <group string="موافقة مسؤول المخازن" attrs="{'invisible': [('warehouse_approval_date', '=', False)]}">
                                    <field name="warehouse_approval_user_id" readonly="1"/>
                                    <field name="warehouse_approval_date" readonly="1"/>
                                </group>
                                <group string="موافقة الموارد البشرية" attrs="{'invisible': [('hr_approval_date', '=', False)]}">
                                    <field name="hr_approval_user_id" readonly="1"/>
                                    <field name="hr_approval_date" readonly="1"/>
                                </group>
                            </group>
                            <group>
                                <group string="معلومات الرفض" attrs="{'invisible': [('rejection_date', '=', False)]}">
                                    <field name="rejection_user_id" readonly="1"/>
                                    <field name="rejection_date" readonly="1"/>
                                </group>
                            </group>
                            <group attrs="{'invisible': [('receipt_confirmation_date', '=', False)]}">
                                <group string="تأكيد استلام المواد">
                                    <field name="receipt_confirmation_user_id" readonly="1"/>
                                    <field name="receipt_confirmation_date" readonly="1"/>
                                    <field name="receipt_notes" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Stationery Request Actions -->
    <record id="action_my_stationery_requests" model="ir.actions.act_window">
        <field name="name">My Stationery Requests</field>
        <field name="res_model">bssic.stationery.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('employee_id.user_id', '=', uid)]</field>
        <field name="context">{'default_employee_id': context.get('default_employee_id', False)}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new stationery request
            </p>
        </field>
    </record>

    <record id="action_all_stationery_requests" model="ir.actions.act_window">
        <field name="name">All Stationery Requests</field>
        <field name="res_model">bssic.stationery.request</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new stationery request
            </p>
        </field>
    </record>

    <record id="action_stationery_requests_to_approve" model="ir.actions.act_window">
        <field name="name">Stationery Requests to Approve</field>
        <field name="res_model">bssic.stationery.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', 'in', ['direct_manager', 'warehouse_approval', 'hr_approval'])]</field>
        <field name="context">{'search_default_to_approve': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No stationery requests to approve
            </p>
        </field>
    </record>

    <record id="action_stationery_requests_warehouse_approval" model="ir.actions.act_window">
        <field name="name">Warehouse Approval Requests</field>
        <field name="res_model">bssic.stationery.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'warehouse_approval')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No stationery requests waiting for warehouse approval
            </p>
        </field>
    </record>

    <!-- Stationery Request Search View -->
    <record id="view_stationery_request_search" model="ir.ui.view">
        <field name="name">bssic.stationery.request.search</field>
        <field name="model">bssic.stationery.request</field>
        <field name="arch" type="xml">
            <search string="Search Stationery Requests">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <separator/>
                <filter string="My Requests" name="my_requests" domain="[('employee_id.user_id', '=', uid)]"/>
                <filter string="To Approve (Manager)" name="to_approve_manager" domain="[('state', '=', 'direct_manager')]" groups="bssic_requests.group_bssic_direct_manager"/>
                <filter string="To Approve (Warehouse)" name="to_approve_warehouse" domain="[('state', '=', 'warehouse_approval')]" groups="bssic_requests.group_bssic_warehouse_manager"/>
                <filter string="To Approve (HR)" name="to_approve_hr" domain="[('state', '=', 'hr_approval')]" groups="bssic_requests.group_bssic_hr_manager"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Submitted" name="submitted" domain="[('state', '=', 'submitted')]"/>
                <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                <filter string="Rejected" name="rejected" domain="[('state', '=', 'rejected')]"/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Department" name="department" context="{'group_by': 'department_id'}"/>
                    <filter string="Status" name="status" context="{'group_by': 'state'}"/>
                    <filter string="Request Date" name="request_date" context="{'group_by': 'request_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Create a new stationery request action -->
    <record id="action_create_stationery_request" model="ir.actions.act_window">
        <field name="name">Stationery Request</field>
        <field name="res_model">bssic.stationery.request</field>
        <field name="view_mode">form</field>
        <field name="target">current</field>
        <field name="context">{'default_employee_id': context.get('default_employee_id', False)}</field>
    </record>

    <!-- Stationery Requests list action -->
    <record id="action_stationery_requests" model="ir.actions.act_window">
        <field name="name">Stationery Requests</field>
        <field name="res_model">bssic.stationery.request</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new stationery request
            </p>
        </field>
    </record>
</odoo>
