from odoo import models, fields, api

class BSSICRequestType(models.Model):
    _name = 'bssic.request.type'
    _description = 'BSSIC Request Type'

    name = fields.Char('Name', required=True)
    code = fields.Char('Code')
    active = fields.<PERSON><PERSON>an('Active', default=True)
    description = fields.Text('Description')
    show_password_fields = fields.<PERSON><PERSON><PERSON>('Show Password Fields')
    show_usb_fields = fields.<PERSON><PERSON><PERSON>('Show USB Fields')
    show_extension_fields = fields.<PERSON><PERSON><PERSON>('Show Extension Fields')
    show_permission_fields = fields.<PERSON><PERSON><PERSON>('Show Permission Fields')
    show_email_fields = fields.<PERSON><PERSON><PERSON>('Show Email Fields')