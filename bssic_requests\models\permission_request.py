from odoo import models, fields, api, _
from odoo.exceptions import UserError

class BSSICPermissionRequest(models.Model):
    _name = 'bssic.permission.request'
    _description = 'BSSIC Permission Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    name = fields.Char(string='Reference', required=True, copy=False, readonly=True, 
                      default=lambda self: _('New'))
    
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True, tracking=True)
    employee_number = fields.Char(related='employee_id.identification_id', string='Employee Number', 
                                 store=True, readonly=True)
    department_id = fields.Many2one(related='employee_id.department_id', string='Department', 
                                   store=True, readonly=True)
    
    request_date = fields.Date(string='Request Date', default=fields.Date.context_today, required=True)
    
    # User information
    user_name = fields.Char(string='User Name', required=True)
    job_title = fields.Char(string='Job Title', required=True)
    
    # Permission details
    permission_type = fields.Selection([
        ('add', 'Add'),
        ('modify', 'Modify'),
        ('delete', 'Delete'),
        ('withdraw', 'Withdraw'),
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
    ], string='Permission Type', required=True)
    
    validity_from = fields.Date(string='Valid From')
    validity_to = fields.Date(string='Valid To')
    
    # Department permissions
    accounting_dept = fields.Boolean(string='Accounting Department')
    accounting_level = fields.Char(string='Accounting Level')
    accounting_group = fields.Char(string='Accounting Group')
    
    internal_audit = fields.Boolean(string='Internal Auditing')
    internal_audit_level = fields.Char(string='Internal Audit Level')
    internal_audit_group = fields.Char(string='Internal Audit Group')
    
    risk_dept = fields.Boolean(string='Risk')
    risk_level = fields.Char(string='Risk Level')
    risk_group = fields.Char(string='Risk Group')
    
    back_office_credits = fields.Boolean(string='Back Office - Credits')
    back_office_credits_level = fields.Char(string='Credits Level')
    back_office_credits_group = fields.Char(string='Credits Group')
    
    back_office_deposits = fields.Boolean(string='Back Office - Deposits')
    back_office_deposits_level = fields.Char(string='Deposits Level')
    back_office_deposits_group = fields.Char(string='Deposits Group')
    
    operations_dept = fields.Boolean(string='Operations Department')
    operations_level = fields.Char(string='Operations Level')
    operations_group = fields.Char(string='Operations Group')
    
    forex_exchange = fields.Boolean(string='Forex Exchange')
    forex_level = fields.Char(string='Forex Level')
    forex_group = fields.Char(string='Forex Group')
    
    banking_operations = fields.Boolean(string='Banking Operations')
    banking_level = fields.Char(string='Banking Level')
    banking_group = fields.Char(string='Banking Group')
    
    personnel_admin = fields.Boolean(string='Personnel & Admin')
    personnel_level = fields.Char(string='Personnel Level')
    personnel_group = fields.Char(string='Personnel Group')
    
    swift = fields.Boolean(string='Swift')
    swift_level = fields.Char(string='Swift Level')
    swift_group = fields.Char(string='Swift Group')
    
    # Transaction limits
    transaction_amount_limit = fields.Float(string='Transaction Amount Limit')
    auth_limit = fields.Float(string='Auth. O.D. Limit')
    max_amount_limit = fields.Float(string='MAX Amount Limit')
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager_approval', 'Direct Manager Approval'),
        ('dept_manager_approval', 'Department Manager Approval'),
        ('it_manager_approval', 'IT Manager Approval'),
        ('assigned', 'Assigned to IT Staff'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)
    
    assigned_to = fields.Many2one('hr.employee', string='Assigned To', tracking=True, ondelete="set null")
    
    @api.model
    def create(self, vals):
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('bssic.permission.request') or _('New')
        return super(BSSICPermissionRequest, self).create(vals)
    
    def action_submit(self):
        self.write({'state': 'submitted'})
    
    def action_direct_manager_approve(self):
        self.write({'state': 'direct_manager_approval'})
    
    def action_dept_manager_approve(self):
        self.write({'state': 'dept_manager_approval'})
    
    def action_it_manager_approve(self):
        self.write({'state': 'it_manager_approval'})
    
    def action_assign(self):
        self.write({'state': 'assigned'})
    
    def action_start_progress(self):
        self.write({'state': 'in_progress'})
    
    def action_complete(self):
        self.write({'state': 'completed'})
    
    def action_reject(self):
        self.write({'state': 'rejected'})