from odoo import models, fields, api, _

class TechnicalCategory(models.Model):
    _name = 'bssic.technical.category'
    _description = 'Technical Request Category'
    _order = 'sequence, name'
    
    name = fields.Char(string='Category Name', required=True)
    code = fields.Char(string='Category Code', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    parent_id = fields.Many2one('bssic.technical.category', string='Parent Category')
    child_ids = fields.One2many('bssic.technical.category', 'parent_id', string='Child Categories')
    description = fields.Text(string='Description')
    active = fields.Boolean(default=True)
