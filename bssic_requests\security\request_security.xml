<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Request Security Groups -->
        <record id="module_bssic_requests_category" model="ir.module.category">
            <field name="name">BSSIC Requests</field>
            <field name="description">User access level for BSSIC Requests</field>
            <field name="sequence">20</field>
        </record>
        
        <!-- Employee Group - Can create and view own requests -->
        <record id="group_bssic_employee" model="res.groups">
            <field name="name">Employee</field>
            <field name="category_id" ref="module_bssic_requests_category"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <!-- Direct Manager Group - Can approve employee requests -->
        <record id="group_bssic_direct_manager" model="res.groups">
            <field name="name">Direct Manager</field>
            <field name="category_id" ref="module_bssic_requests_category"/>
            <field name="implied_ids" eval="[(4, ref('group_bssic_employee'))]"/>
        </record>
        
        <!-- Audit Manager Group - Can approve after direct manager -->
        <record id="group_bssic_audit_manager" model="res.groups">
            <field name="name">Audit Manager</field>
            <field name="category_id" ref="module_bssic_requests_category"/>
            <field name="implied_ids" eval="[(4, ref('group_bssic_employee'))]"/>
        </record>
        
        <!-- IT Manager Group - Can approve and assign requests -->
        <record id="group_bssic_it_manager" model="res.groups">
            <field name="name">IT Manager</field>
            <field name="category_id" ref="module_bssic_requests_category"/>
            <field name="implied_ids" eval="[(4, ref('group_bssic_employee'))]"/>
        </record>
        
        <!-- IT Staff Group - Can implement requests -->
        <record id="group_bssic_it_staff" model="res.groups">
            <field name="name">IT Staff</field>
            <field name="category_id" ref="module_bssic_requests_category"/>
            <field name="implied_ids" eval="[(4, ref('group_bssic_employee'))]"/>
        </record>
        
        <!-- Module Visibility Rule -->
        <record id="rule_show_module_based_on_groups" model="ir.rule">
            <field name="name">Show Module Based on Groups</field>
            <field name="model_id" ref="model_bssic_request"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_bssic_employee'))]"/>
        </record>
        
        <!-- Record Rules -->
        <record id="rule_employee_own_requests" model="ir.rule">
            <field name="name">Employee: Own Requests Only</field>
            <field name="model_id" ref="model_bssic_request"/>
            <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_bssic_employee'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="rule_direct_manager_team_requests" model="ir.rule">
            <field name="name">Direct Manager: Team Requests</field>
            <field name="model_id" ref="model_bssic_request"/>
            <field name="domain_force">[('employee_id.parent_id.user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_bssic_direct_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="rule_audit_manager_all_requests" model="ir.rule">
            <field name="name">Audit Manager: All Requests</field>
            <field name="model_id" ref="model_bssic_request"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_bssic_audit_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="rule_it_manager_all_requests" model="ir.rule">
            <field name="name">IT Manager: All Requests</field>
            <field name="model_id" ref="model_bssic_request"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_bssic_it_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="rule_it_staff_assigned_requests" model="ir.rule">
            <field name="name">IT Staff: Assigned Requests</field>
            <field name="model_id" ref="model_bssic_request"/>
            <field name="domain_force">['|', ('assigned_to.user_id', '=', user.id), ('state', 'in', ['completed', 'rejected'])]</field>
            <field name="groups" eval="[(4, ref('group_bssic_it_staff'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
    </data>
</odoo>