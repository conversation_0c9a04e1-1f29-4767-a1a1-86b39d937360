from odoo import models, fields, api, _
from lxml import etree


class BSSICRequest(models.Model):
    _inherit = 'bssic.request'

    # Permission Request Fields
    permission_type = fields.Selection([
        ('add', 'Add'),
        ('modify', 'Modify'),
        ('delete', 'Delete'),
        ('withdraw', 'Withdraw'),
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
    ], string='Permission Type', tracking=True)
    user_name = fields.Char('User Name', tracking=True)
    validity_from = fields.Date('Valid From', tracking=True)
    validity_to = fields.Date('Valid To', tracking=True)

    # Department permissions
    accounting_dept = fields.Boolean(string='Accounting Department', tracking=True)
    accounting_level = fields.Char(string='Accounting Level', tracking=True)
    accounting_group = fields.Char(string='Accounting Group', tracking=True)

    internal_audit = fields.Boolean(string='Internal Auditing', tracking=True)
    internal_audit_level = fields.Char(string='Internal Audit Level', tracking=True)
    internal_audit_group = fields.Char(string='Internal Audit Group', tracking=True)

    risk_dept = fields.Boolean(string='Risk', tracking=True)
    risk_level = fields.Char(string='Risk Level', tracking=True)
    risk_group = fields.Char(string='Risk Group', tracking=True)

    back_office_credits = fields.Boolean(string='Back Office - Credits', tracking=True)
    back_office_credits_level = fields.Char(string='Credits Level', tracking=True)
    back_office_credits_group = fields.Char(string='Credits Group', tracking=True)

    back_office_deposits = fields.Boolean(string='Back Office - Deposits', tracking=True)
    back_office_deposits_level = fields.Char(string='Deposits Level', tracking=True)
    back_office_deposits_group = fields.Char(string='Deposits Group', tracking=True)

    operations_dept = fields.Boolean(string='Operations Department', tracking=True)
    operations_level = fields.Char(string='Operations Level', tracking=True)
    operations_group = fields.Char(string='Operations Group', tracking=True)

    forex_exchange = fields.Boolean(string='Forex Exchange', tracking=True)
    forex_level = fields.Char(string='Forex Level', tracking=True)
    forex_group = fields.Char(string='Forex Group', tracking=True)

    banking_operations = fields.Boolean(string='Banking Operations', tracking=True)
    banking_level = fields.Char(string='Banking Level', tracking=True)
    banking_group = fields.Char(string='Banking Group', tracking=True)

    personnel_admin = fields.Boolean(string='Personnel & Admin', tracking=True)
    personnel_level = fields.Char(string='Personnel Level', tracking=True)
    personnel_group = fields.Char(string='Personnel Group', tracking=True)

    swift = fields.Boolean(string='Swift', tracking=True)
    swift_level = fields.Char(string='Swift Level', tracking=True)
    swift_group = fields.Char(string='Swift Group', tracking=True)

    # Transaction limits
    transaction_amount_limit = fields.Float(string='Transaction Amount Limit', tracking=True)
    auth_limit = fields.Float(string='Auth. O.D. Limit', tracking=True)
    max_amount_limit = fields.Float(string='MAX Amount Limit', tracking=True)

    def _get_it_staff_domain(self):
        """Get domain for IT Staff employees"""
        try:
            it_staff_group_id = self.env.ref('bssic_requests.group_bssic_it_staff').id
            it_staff_users = self.env['res.users'].search([('groups_id', 'in', [it_staff_group_id])])
            it_staff_employees = self.env['hr.employee'].search([('user_id', 'in', it_staff_users.ids)])

            # Also include employees in IT department
            it_dept_employees = self.env['hr.employee'].search([
                '|',
                ('department_id.name', 'ilike', 'IT'),
                ('department_id.name', 'ilike', 'Information Technology')
            ])

            all_it_employees = it_staff_employees | it_dept_employees

            if all_it_employees:
                return [('id', 'in', all_it_employees.ids)]
            else:
                # If no IT employees found, return empty domain
                return [('id', '=', False)]
        except:
            # Fallback to department-based search if group doesn't exist
            return [
                '|',
                ('department_id.name', 'ilike', 'IT'),
                ('department_id.name', 'ilike', 'Information Technology')
            ]

    def _get_employee_domain(self):
        """Get domain for employee selection based on user permissions"""
        current_user = self.env.user
        is_manager = current_user.has_group('bssic_requests.group_bssic_direct_manager')

        if is_manager:
            # Managers can see employees in their department who report to them
            manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
            if manager_employee:
                # Include the manager themselves and their direct reports in the same department
                domain = [
                    '|',
                    ('id', '=', manager_employee.id),  # Manager themselves
                    '&',
                    ('department_id', '=', manager_employee.department_id.id),  # Same department
                    ('parent_id', '=', manager_employee.id)  # Reports to manager
                ]
                return domain
        else:
            # Regular employees can only see themselves
            employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
            if employee:
                return [('id', '=', employee.id)]

        return [('id', '=', -1)]  # No access

    @api.model
    def fields_view_get(self, view_id=None, view_type='form', toolbar=False, submenu=False):
        res = super(BSSICRequest, self).fields_view_get(view_id=view_id, view_type=view_type, toolbar=toolbar, submenu=submenu)
        if view_type == 'form':
            doc = etree.XML(res['arch'])

            # Set domain for assigned_to field
            for node in doc.xpath("//field[@name='assigned_to']"):
                node.set('domain', str(self._get_it_staff_domain()))

            # Check if user is a manager
            is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')

            # Set domain for employee_id field based on manager permissions
            employee_domain = self._get_employee_domain()
            employees = self.env['hr.employee'].search(employee_domain)

            # Set domain for employee_id field
            for node in doc.xpath("//field[@name='employee_id']"):
                if is_manager:
                    node.set('domain', str([('id', 'in', employees.ids)]))
                else:
                    current_user = self.env.user
                    employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if employee:
                        node.set('domain', str([('id', '=', employee.id)]))
                    else:
                        node.set('domain', str([('id', '=', -1)]))

                # Make the field editable in draft state for all users
                attrs = {'readonly': [('state', '!=', 'draft')]}
                node.set('attrs', str(attrs))

            # Always make request_type_id readonly when a specific request type is selected
            context = self.env.context
            if context.get('default_request_type_code') or context.get('default_request_type_id'):
                for node in doc.xpath("//field[@name='request_type_id']"):
                    node.set('readonly', '1')
                    node.set('force_save', '1')

            res['arch'] = etree.tostring(doc, encoding='unicode')
        return res

    @api.onchange('state')
    def _onchange_state(self):
        if self.state == 'assigned':
            return {'domain': {'assigned_to': self._get_it_staff_domain()}}

    @api.onchange('assigned_to')
    def _onchange_assigned_to(self):
        """Validate that assigned employee is IT staff"""
        if self.assigned_to:
            it_domain = self._get_it_staff_domain()
            # Check if the selected employee matches the IT domain
            if it_domain and it_domain[0][0] == 'id' and it_domain[0][1] == 'in':
                allowed_ids = it_domain[0][2]
                if self.assigned_to.id not in allowed_ids:
                    self.assigned_to = False
                    return {
                        'warning': {
                            'title': _('Invalid Selection'),
                            'message': _('Please select an employee from the IT department or with IT Staff permissions.')
                        }
                    }

    @api.model
    def _search(self, args, offset=0, limit=None, order=None, count=False, access_rights_uid=None):
        context = self._context or {}
        if self._name == 'hr.employee' and context.get('it_staff_only'):
            args = args + self.env['bssic.request']._get_it_staff_domain()
        return super(BSSICRequest, self)._search(args, offset, limit, order, count, access_rights_uid)
