<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Technical Request Form View -->
    <record id="view_bssic_technical_request_form_new" model="ir.ui.view">
        <field name="name">bssic.technical.request.form.new</field>
        <field name="model">bssic.request</field>
        <field name="inherit_id" ref="bssic_requests.view_bssic_request_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <!-- Override the header to customize buttons for technical requests -->
            <xpath expr="//header" position="replace">
                <header>
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"/>
                    <button name="action_approve_direct_manager" string="Approve (Direct Manager)" type="object" class="oe_highlight"
                            states="direct_manager" groups="bssic_requests.group_bssic_direct_manager"/>
                    <!-- No Audit Manager approval for technical requests -->
                    <button name="action_approve_it_manager" string="Approve (IT Manager)" type="object" class="oe_highlight"
                            states="it_manager" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_assign" string="Assign to IT Staff" type="object" class="oe_highlight"
                            states="assigned" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_complete" string="Mark as Completed" type="object" class="oe_highlight"
                            states="in_progress" groups="bssic_requests.group_bssic_it_staff"/>
                    <button name="action_reject" string="Reject" type="object" class="btn-danger"
                            states="direct_manager,it_manager,assigned,in_progress"/>
                    <field name="is_technical" invisible="1"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,direct_manager,it_manager,assigned,in_progress,completed"/>
                </header>
            </xpath>

            <!-- Override the request details page to show technical fields -->
            <xpath expr="//page[@name='request_details']" position="replace">
                <page string="Request Details" name="request_details">
                    <group>
                        <group>
                            <field name="request_nature" widget="radio" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="technical_category_id" attrs="{'required': [('is_technical', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                        </group>
                        <group>
                            <field name="technical_subcategory_id" attrs="{'required': [('is_technical', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="priority" widget="priority" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                    </group>
                </page>
            </xpath>

            <!-- Make sure employee fields are editable -->
            <xpath expr="//field[@name='is_manager']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='employee_number']" position="attributes">
                <attribute name="attrs">{'readonly': [('state', '!=', 'draft')]}</attribute>
            </xpath>
            <xpath expr="//field[@name='employee_id']" position="attributes">
                <attribute name="attrs">{'readonly': [('state', '!=', 'draft')]}</attribute>
                <attribute name="options">{'no_create': True}</attribute>
            </xpath>
        </field>
    </record>

    <!-- Technical Request Tree View -->
    <record id="view_bssic_technical_request_tree_new" model="ir.ui.view">
        <field name="name">bssic.technical.request.tree.new</field>
        <field name="model">bssic.request</field>
        <field name="arch" type="xml">
            <tree string="Technical Requests" decoration-info="state == 'draft'" decoration-muted="state == 'rejected'" decoration-success="state == 'completed'">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="technical_category_id"/>
                <field name="priority"/>
                <field name="request_date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Update the technical request actions to use the new views -->
    <record id="action_technical_support_requests_new" model="ir.actions.act_window">
        <field name="name">Technical Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'technical')]</field>
        <field name="context">{'default_request_type_code': 'technical', 'default_is_technical': True, 'default_request_nature': 'technical', 'default_name': 'Technical Request', 'form_view_ref': 'bssic_requests.view_bssic_technical_request_form_new', 'allow_employee_selection': True}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new technical request
            </p>
        </field>
    </record>

    <record id="action_my_technical_requests_new" model="ir.actions.act_window">
        <field name="name">My Technical Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'technical'), ('employee_id.user_id', '=', uid)]</field>
        <field name="context">{'default_request_type_code': 'technical', 'default_is_technical': True, 'default_request_nature': 'technical', 'default_name': 'Technical Request', 'form_view_ref': 'bssic_requests.view_bssic_technical_request_form_new'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                View your technical requests
            </p>
        </field>
    </record>

    <record id="action_all_technical_requests_new" model="ir.actions.act_window">
        <field name="name">All Technical Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'technical')]</field>
        <field name="context">{'default_request_type_code': 'technical', 'default_is_technical': True, 'default_request_nature': 'technical', 'default_name': 'Technical Request', 'form_view_ref': 'bssic_requests.view_bssic_technical_request_form_new'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                View all technical requests
            </p>
        </field>
    </record>
</odoo>
